Feature: Seamless Protocol Migration
  As a user of WebOTR
  I want to seamlessly switch between protocols during conversations
  So that I can adapt to changing conversation needs without losing security

  Background:
    Given I have WebOTR installed with both OTR and MLS support
    And protocol migration is enabled in my settings
    And I have the following contacts:
      | name    | supports_otr | supports_mls |
      | Alice   | true         | true         |
      | Bob     | true         | true         |
      | Charlie | true         | true         |
      | <PERSON>   | true         | false        |

  Scenario: Adding participant to OTR conversation triggers MLS migration
    Given I have an active OTR conversation with <PERSON>
    And we have exchanged the following messages:
      | sender | content           | timestamp |
      | me     | Hello Alice       | 10:00     |
      | Alice  | Hi there!         | 10:01     |
      | me     | How are you?      | 10:02     |
      | Alice  | I'm doing well    | 10:03     |
    When I invite <PERSON> to join the conversation
    And <PERSON> accepts the invitation
    Then the system should detect the need for MLS
    And prompt me with a migration dialog showing:
      | field | value |
      | current_protocol | OTR v3 |
      | target_protocol | MLS |
      | reason | Group conversation requires MLS |
      | participants | me, <PERSON>, <PERSON> |
    And I should be able to confirm the migration
    And existing message history should be preserved
    And all three participants should establish MLS group keys
    And future messages should use MLS encryption

  Scenario: User-initiated protocol switch from OTR to MLS
    Given I have a two-party OTR conversation with <PERSON>
    And we have been chatting for 10 minutes
    When I manually select "Switch to MLS" from the protocol menu
    And <PERSON> receives the migration request
    And <PERSON> accepts the protocol change
    Then the conversation should migrate to MLS
    And I should see a migration progress indicator
    And message continuity should be maintained
    And security indicators should update to show MLS
    And both parties should confirm the successful migration

  Scenario: User-initiated protocol switch from MLS to OTR
    Given I have a two-party MLS conversation with Alice
    And I want to enable deniability for sensitive topics
    When I manually select "Switch to OTR" from the protocol menu
    And Alice receives the migration request
    And Alice accepts the protocol change
    Then the conversation should migrate to OTR
    And I should see a migration progress indicator
    And message continuity should be maintained
    And security indicators should update to show OTR with deniability
    And both parties should confirm the successful migration

  Scenario: Graceful migration failure handling
    Given I have an OTR conversation with Alice
    When I try to add Bob to create a group
    But Bob's client crashes during the MLS setup
    Then the system should detect the migration failure
    And display an error message explaining the issue
    And offer the following recovery options:
      | option | description |
      | retry | Retry the migration process |
      | continue_otr | Continue with separate OTR conversations |
      | wait_and_retry | Wait for Bob to reconnect and retry |
    And maintain the existing OTR session with Alice
    And not leave the conversation in an inconsistent state

  Scenario: Migration with participant capability limitations
    Given I have an OTR conversation with Alice
    When I try to add Diana who doesn't support MLS
    Then the system should explain the limitation clearly
    And offer alternative solutions:
      | solution | description |
      | exclude_diana | Create MLS group without Diana |
      | separate_conversations | Keep OTR with Alice, separate chat with Diana |
      | upgrade_request | Ask Diana to upgrade her client |
      | group_without_migration | Use platform's native group chat |
    And allow me to choose the best option for my needs
    And maintain the existing OTR session with Alice

  Scenario: Migration rollback on timeout
    Given I initiate a migration from OTR to MLS with Alice
    And the migration process starts successfully
    But Alice's client becomes unresponsive during key exchange
    When the migration timeout of 30 seconds is reached
    Then the system should automatically rollback to OTR
    And notify me about the rollback with the reason
    And restore the original OTR session state
    And allow me to retry the migration later

  Scenario: Partial migration recovery
    Given I have an OTR conversation with Alice and Bob
    And I initiate migration to MLS
    And Alice successfully migrates to MLS
    But Bob fails during the Welcome message processing
    When the partial migration is detected
    Then the system should offer recovery options:
      | option | description |
      | complete_with_alice | Continue MLS with Alice only |
      | rollback_all | Rollback everyone to OTR |
      | retry_bob | Retry Bob's migration |
    And maintain conversation continuity regardless of choice
    And ensure no participant is left in an inconsistent state

  Scenario: Migration with message ordering preservation
    Given I have an active OTR conversation with Alice
    And we are actively exchanging messages during migration
    When I add Bob and trigger MLS migration
    And messages are sent during the migration process
    Then all messages should be properly ordered
    And no messages should be lost during migration
    And message timestamps should be preserved
    And the conversation flow should remain coherent

  Scenario: Migration security property validation
    Given I have an OTR conversation with Alice
    When I migrate to MLS by adding Bob
    Then the system should validate that:
      | property | status |
      | confidentiality | preserved |
      | integrity | preserved |
      | authenticity | preserved |
      | forward_secrecy | preserved |
      | deniability | lost (as expected) |
      | post_compromise_security | gained |
    And clearly communicate the security property changes
    And require my confirmation before proceeding

  Scenario: Migration with custom security requirements
    Given I have security requirements configured for "high confidentiality"
    And I have an OTR conversation with Alice
    When I attempt to migrate to MLS
    Then the system should validate the migration against my requirements
    And ensure the target protocol meets all security criteria
    And warn me if any requirements would be compromised
    And allow me to adjust requirements or cancel migration

  Scenario: Bulk migration for multiple conversations
    Given I have active OTR conversations with:
      | participant | message_count | duration |
      | Alice       | 25           | 2 hours  |
      | Bob         | 15           | 1 hour   |
      | Charlie     | 8            | 30 mins  |
    When I select "Migrate all to MLS" from the settings
    Then the system should show a migration plan for each conversation
    And allow me to customize the migration for each participant
    And execute migrations in parallel where possible
    And provide progress updates for each migration
    And handle individual failures without affecting others

  Scenario: Migration with conversation archival
    Given I have a long-running OTR conversation with Alice
    And we have 500+ messages over 3 months
    When I migrate the conversation to MLS
    Then the system should offer to archive the OTR history
    And create a new MLS conversation thread
    And provide seamless access to both old and new messages
    And maintain searchability across the migration boundary
    And preserve all metadata and timestamps

  Scenario: Migration notification and consent
    Given Alice initiates a migration from OTR to MLS
    And I receive the migration request
    When I review the migration details
    Then I should see:
      | detail | value |
      | initiator | Alice |
      | current_protocol | OTR v3 |
      | target_protocol | MLS |
      | reason | User requested upgrade |
      | security_changes | Deniability lost, PCS gained |
      | estimated_duration | 5-10 seconds |
    And I should be able to accept or decline
    And if I decline, Alice should be notified with my reason
    And the conversation should continue with the current protocol

  Scenario: Migration with platform-specific considerations
    Given I'm using WebOTR on Discord
    And I have an OTR conversation with Alice
    When I migrate to MLS by adding Bob
    Then the system should handle Discord's message formatting
    And ensure MLS messages appear correctly in Discord's UI
    And maintain compatibility with Discord's features
    And preserve Discord-specific metadata where possible
    And handle Discord's rate limiting during migration

  Scenario: Migration audit and compliance logging
    Given I have compliance logging enabled
    When any protocol migration occurs
    Then the system should log:
      | field | description |
      | timestamp | When the migration occurred |
      | participants | All involved parties |
      | protocols | From and to protocols |
      | reason | Why the migration was triggered |
      | outcome | Success or failure details |
      | security_impact | Changes in security properties |
    And make the audit trail available for compliance review
    And ensure logs are tamper-evident and properly signed
