Feature: Intelligent Protocol Selection
  As a user of WebOTR
  I want the system to automatically select the appropriate protocol
  So that I get optimal security and functionality for each conversation

  Background:
    Given I have WebOTR installed and configured
    And I have the following contacts:
      | name    | supports_otr | supports_mls |
      | Alice   | true         | true         |
      | Bob     | true         | true         |
      | <PERSON> | true         | false        |
      | <PERSON>   | false        | true         |

  Scenario: Starting a two-party conversation with dual protocol support
    Given I want to start a conversation with <PERSON>
    And <PERSON> supports both OTR and MLS
    And my policy is set to "prefer OTR for 2-party conversations"
    When I initiate the conversation
    Then OTR should be selected as the protocol
    And I should see "OTR v3" in the security indicator
    And the conversation should support deniability
    And I should be able to send encrypted messages

  Scenario: Starting a group conversation requires MLS
    Given I want to start a conversation with <PERSON> and <PERSON>
    And both <PERSON> and <PERSON> support MLS
    When I initiate the group conversation
    Then MLS should be selected automatically
    And I should see "<PERSON>" in the security indicator
    And the conversation should support group messaging
    And all participants should receive group keys

  Scenario: User preference overrides default for two-party conversations
    Given I want to start a conversation with <PERSON>
    And <PERSON> supports both OTR and MLS
    And my user preference is set to "MLS"
    When I initiate the conversation
    Then <PERSON> should be selected as the protocol
    And I should see "MLS" in the security indicator
    And the reason should be "USER_PREFERENCE"

  Scenario: Mixed capability handling for group conversations
    Given I want to start a conversation with <PERSON>, <PERSON>, and <PERSON>
    And <PERSON> supports both OTR and MLS
    And <PERSON> supports both OTR and MLS
    But <PERSON> only supports OTR
    When I attempt to initiate the group conversation
    Then the system should display a capability warning
    And suggest the following alternatives:
      | option | description |
      | separate_conversations | Create separate OTR conversations with each participant |
      | exclude_participant | Continue with MLS excluding Charlie |
      | upgrade_request | Ask Charlie to upgrade their client |
    And I should be able to choose my preferred option

  Scenario: Existing session preservation
    Given I have an active OTR conversation with Alice
    And we have exchanged 5 messages
    When I restart the application
    And reconnect to the conversation
    Then OTR should remain the selected protocol
    And the reason should be "EXISTING_SESSION"
    And message history should be preserved
    And the session should continue seamlessly

  Scenario: Security requirement override
    Given I want to start a conversation with Alice
    And Alice supports both OTR and MLS
    And I have a security requirement for "deniability"
    When I initiate the conversation
    Then OTR should be selected as the protocol
    And the reason should be "SECURITY_REQUIREMENT_DENIABILITY"
    And deniability should be confirmed as available

  Scenario: Post-compromise security requirement
    Given I want to start a conversation with Alice
    And Alice supports both OTR and MLS
    And I have a security requirement for "post-compromise security"
    When I initiate the conversation
    Then MLS should be selected as the protocol
    And the reason should be "SECURITY_REQUIREMENT_PCS"
    And post-compromise security should be confirmed as available

  Scenario: Meeting conversation type selection
    Given I want to start a meeting with Alice and Bob
    And both participants support MLS
    And the conversation type is set to "meeting"
    When I initiate the meeting
    Then MLS should be selected as the protocol
    And the reason should be "CONVERSATION_TYPE_MEETING"
    And group messaging features should be available

  Scenario: Fallback when no protocols are mutually supported
    Given I want to start a conversation with Diana
    And Diana only supports MLS
    But I only support OTR
    When I attempt to initiate the conversation
    Then the system should display an incompatibility error
    And suggest upgrading my client to support MLS
    And offer to notify Diana about the compatibility issue

  Scenario: Protocol selection with security priority settings
    Given I want to start a conversation with Alice
    And Alice supports both OTR and MLS
    And my security priority is set to "privacy"
    When I initiate the conversation
    Then OTR should be selected as the protocol
    And the reason should be "SECURITY_PRIORITY_PRIVACY"

  Scenario: Protocol selection with balanced security priority
    Given I want to start a conversation with Alice
    And Alice supports both OTR and MLS
    And my security priority is set to "balanced"
    When I initiate the conversation
    Then MLS should be selected as the protocol
    And the reason should be "SECURITY_PRIORITY_BALANCED"

  Scenario: Large group conversation optimization
    Given I want to start a conversation with 50 participants
    And all participants support MLS
    When I initiate the large group conversation
    Then MLS should be selected as the protocol
    And the system should optimize for large group performance
    And tree-based key management should be used

  Scenario: Protocol selection confidence levels
    Given I want to start a conversation with Alice
    And Alice supports both OTR and MLS
    And multiple selection criteria apply
    When the protocol selection is made
    Then the system should provide a confidence level
    And list alternative protocols with their reasons
    And allow me to override the selection if confidence is low

  Scenario: Capability negotiation timeout
    Given I want to start a conversation with Alice
    And Alice's client is slow to respond
    When capability negotiation takes longer than 10 seconds
    Then the system should timeout gracefully
    And fall back to the most compatible protocol
    And notify me about the degraded capability detection

  Scenario: Protocol selection audit trail
    Given I have protocol selection logging enabled
    When any protocol selection is made
    Then the system should log the selection decision
    And include all factors that influenced the decision
    And provide a timestamp and session identifier
    And make the audit trail available for review
