/**
 * Unified Session Manager
 * 
 * Manages sessions that can use either OTR or MLS protocols,
 * with intelligent protocol selection and seamless migration capabilities.
 */

import { EventEmitter } from 'events';
import { ProtocolSelector } from '../protocol/ProtocolSelector.js';
import { OTRProtocol } from '../protocol/OTRProtocol.js';
import { MLSProtocol } from '../protocol/MLSProtocol.js';
import { ProtocolError, MigrationError } from '../protocol/ProtocolInterface.js';

/**
 * Unified session that can handle both OTR and MLS protocols
 */
export class UnifiedSession extends EventEmitter {
  /**
   * Create a unified session
   * @param {Object} options - Session options
   */
  constructor(options = {}) {
    super();

    this.options = {
      // Protocol selection policy
      policy: {
        preferOTRFor2Party: true,
        requireMLSForGroups: true,
        allowProtocolMigration: true,
        respectUserPreference: true,
        securityPriority: 'balanced'
      },
      
      // Session configuration
      autoConnect: false,
      progressUpdates: true,
      verificationRequired: false,
      migrationTimeout: 30000,
      
      // Override with provided options
      ...options
    };

    // Core components
    this.protocolSelector = new ProtocolSelector(this.options.policy);
    this.currentProtocol = null;
    this.protocolHandlers = new Map();
    
    // Session state
    this.sessionId = this.generateSessionId();
    this.state = 'UNINITIALIZED';
    this.participants = [];
    this.conversationContext = null;
    
    // Migration state
    this.migrationState = null;
    this.migrationTimeout = null;
    
    // Performance metrics
    this.metrics = {
      sessionStartTime: null,
      sessionDuration: null,
      protocolSwitches: 0,
      messagesSent: 0,
      messagesReceived: 0,
      migrationAttempts: 0,
      migrationSuccesses: 0
    };

    // Message history
    this.messageHistory = [];
    this.maxHistorySize = options.maxHistorySize || 1000;
  }

  /**
   * Initialize session with participants
   * @param {Array<string>} participants - Participant identifiers
   * @param {Object} options - Initialization options
   */
  async initializeSession(participants, options = {}) {
    try {
      this.metrics.sessionStartTime = Date.now();
      this.participants = [...participants];
      this.setState('INITIALIZING');

      // Build conversation context
      this.conversationContext = this.buildConversationContext(participants, options);
      
      // Select appropriate protocol
      const selection = this.protocolSelector.selectProtocol(this.conversationContext);
      
      this.emit('protocolSelected', {
        protocol: selection.protocol,
        reason: selection.reason,
        confidence: selection.confidence,
        alternatives: selection.alternatives
      });

      // Create and initialize protocol handler
      this.currentProtocol = await this.createProtocolHandler(selection.protocol, options);
      await this.currentProtocol.initializeSession(participants, options);

      // Set up protocol event forwarding
      this.setupProtocolEventHandlers();

      this.setState('ACTIVE');
      this.emit('sessionInitialized', {
        sessionId: this.sessionId,
        protocol: selection.protocol,
        participants: this.participants,
        capabilities: this.currentProtocol.getCapabilities()
      });

    } catch (error) {
      this.setState('ERROR');
      this.emit('sessionError', { error: error.message, phase: 'INITIALIZATION' });
      throw error;
    }
  }

  /**
   * Send a message through the current protocol
   * @param {string} content - Message content
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Message result
   */
  async sendMessage(content, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active for messaging', 'INVALID_STATE');
    }

    if (this.migrationState) {
      throw new ProtocolError('Cannot send messages during protocol migration', 'MIGRATION_IN_PROGRESS');
    }

    try {
      const messageResult = await this.currentProtocol.sendMessage(content, context);
      
      // Add to message history
      this.addToMessageHistory({
        type: 'sent',
        content,
        protocol: this.currentProtocol.type,
        timestamp: new Date().toISOString(),
        encrypted: messageResult.encrypted,
        ...messageResult
      });

      this.metrics.messagesSent++;
      
      this.emit('messageSent', {
        content,
        protocol: this.currentProtocol.type,
        encrypted: messageResult.encrypted
      });

      return messageResult;

    } catch (error) {
      this.emit('messageError', { error: error.message, type: 'SEND' });
      throw error;
    }
  }

  /**
   * Receive and process a message
   * @param {Object} message - Received message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Processed message result
   */
  async receiveMessage(message, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active for receiving messages', 'INVALID_STATE');
    }

    try {
      const messageResult = await this.currentProtocol.receiveMessage(message, context);
      
      // Add to message history
      this.addToMessageHistory({
        type: 'received',
        content: messageResult.content,
        sender: messageResult.sender,
        protocol: this.currentProtocol.type,
        timestamp: messageResult.timestamp,
        verified: messageResult.verified,
        ...messageResult
      });

      this.metrics.messagesReceived++;
      
      this.emit('messageReceived', {
        content: messageResult.content,
        sender: messageResult.sender,
        protocol: this.currentProtocol.type,
        verified: messageResult.verified
      });

      return messageResult;

    } catch (error) {
      this.emit('messageError', { error: error.message, type: 'RECEIVE' });
      throw error;
    }
  }

  /**
   * Add a participant to the session
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async addParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE');
    }

    try {
      // Check if current protocol supports adding participants
      if (!this.currentProtocol.supportsFeature('groupMessaging')) {
        // Need to migrate to MLS
        await this.migrateProtocol('MLS', 'PARTICIPANT_ADDITION');
      }

      await this.currentProtocol.addParticipant(participant);
      this.participants.push(participant);

      this.emit('participantAdded', {
        participant,
        protocol: this.currentProtocol.type,
        totalParticipants: this.participants.length
      });

    } catch (error) {
      this.emit('participantError', { error: error.message, type: 'ADD', participant });
      throw error;
    }
  }

  /**
   * Remove a participant from the session
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async removeParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE');
    }

    const participantIndex = this.participants.indexOf(participant);
    if (participantIndex === -1) {
      throw new ProtocolError('Participant not in session', 'PARTICIPANT_NOT_FOUND');
    }

    try {
      await this.currentProtocol.removeParticipant(participant);
      this.participants.splice(participantIndex, 1);

      // Check if we should migrate back to OTR for 2-party conversation
      if (this.participants.length === 2 && this.currentProtocol.type === 'MLS' && this.options.policy.preferOTRFor2Party) {
        this.emit('migrationSuggested', {
          reason: 'TWO_PARTY_OPTIMIZATION',
          fromProtocol: 'MLS',
          toProtocol: 'OTR'
        });
      }

      this.emit('participantRemoved', {
        participant,
        protocol: this.currentProtocol.type,
        totalParticipants: this.participants.length
      });

    } catch (error) {
      this.emit('participantError', { error: error.message, type: 'REMOVE', participant });
      throw error;
    }
  }

  /**
   * Migrate to a different protocol
   * @param {string} toProtocol - Target protocol
   * @param {string} reason - Migration reason
   * @returns {Promise<void>}
   */
  async migrateProtocol(toProtocol, reason = 'USER_REQUEST') {
    if (this.migrationState) {
      throw new MigrationError('Migration already in progress', this.currentProtocol.type, toProtocol);
    }

    const fromProtocol = this.currentProtocol.type;
    
    // Check if migration is feasible
    const feasibility = this.protocolSelector.canMigrate(fromProtocol, toProtocol, {
      participantCount: this.participants.length,
      participants: this.participants,
      capabilities: this.getParticipantCapabilities(),
      reason
    });

    if (!feasibility.canMigrate) {
      throw new MigrationError(`Migration not feasible: ${feasibility.reason}`, fromProtocol, toProtocol);
    }

    try {
      this.metrics.migrationAttempts++;
      this.migrationState = {
        fromProtocol,
        toProtocol,
        reason,
        startTime: Date.now(),
        phase: 'STARTING'
      };

      this.emit('migrationStarted', { ...this.migrationState });

      // Set migration timeout
      this.migrationTimeout = setTimeout(() => {
        this.handleMigrationTimeout();
      }, this.options.migrationTimeout);

      // Execute migration plan
      const migrationPlan = this.protocolSelector.planMigration(fromProtocol, toProtocol, {
        participantCount: this.participants.length,
        participants: this.participants,
        reason
      });

      await this.executeMigrationPlan(migrationPlan);

      // Migration successful
      clearTimeout(this.migrationTimeout);
      this.migrationTimeout = null;
      
      this.metrics.migrationSuccesses++;
      this.metrics.protocolSwitches++;
      
      const migrationDuration = Date.now() - this.migrationState.startTime;
      this.migrationState = null;

      this.emit('migrationCompleted', {
        fromProtocol,
        toProtocol,
        reason,
        duration: migrationDuration,
        preservedProperties: feasibility.preservedProperties,
        lostProperties: feasibility.lostProperties
      });

    } catch (error) {
      await this.handleMigrationFailure(error);
      throw error;
    }
  }

  /**
   * Execute migration plan
   * @param {Object} migrationPlan - Migration plan
   * @private
   */
  async executeMigrationPlan(migrationPlan) {
    const { fromProtocol, toProtocol, steps } = migrationPlan;

    for (const step of steps) {
      this.migrationState.phase = step.step;
      this.emit('migrationProgress', {
        phase: step.step,
        description: step.description
      });

      switch (step.step) {
        case 'VALIDATE_PREREQUISITES':
          await this.validateMigrationPrerequisites(toProtocol);
          break;
        
        case 'NOTIFY_PARTICIPANTS':
          await this.notifyParticipantsOfMigration(fromProtocol, toProtocol);
          break;
        
        case 'PREPARE_NEW_PROTOCOL':
          await this.prepareNewProtocol(toProtocol);
          break;
        
        case 'EXCHANGE_KEYS':
          await this.exchangeKeys(toProtocol);
          break;
        
        case 'VERIFY_SETUP':
          await this.verifyNewProtocolSetup();
          break;
        
        case 'MIGRATE_STATE':
          await this.migrateConversationState(fromProtocol, toProtocol);
          break;
        
        case 'ACTIVATE_PROTOCOL':
          await this.activateNewProtocol();
          break;
        
        case 'CLEANUP_OLD_PROTOCOL':
          await this.cleanupOldProtocol();
          break;
        
        default:
          console.warn(`Unknown migration step: ${step.step}`);
      }
    }
  }

  /**
   * Handle migration timeout
   * @private
   */
  async handleMigrationTimeout() {
    const error = new MigrationError(
      'Migration timed out',
      this.migrationState.fromProtocol,
      this.migrationState.toProtocol
    );
    
    await this.handleMigrationFailure(error);
  }

  /**
   * Handle migration failure
   * @param {Error} error - Migration error
   * @private
   */
  async handleMigrationFailure(error) {
    if (this.migrationTimeout) {
      clearTimeout(this.migrationTimeout);
      this.migrationTimeout = null;
    }

    const migrationInfo = { ...this.migrationState };
    this.migrationState = null;

    // Attempt rollback
    try {
      await this.rollbackMigration();
      
      this.emit('migrationFailed', {
        ...migrationInfo,
        error: error.message,
        rollbackSuccessful: true
      });
      
    } catch (rollbackError) {
      this.setState('ERROR');
      
      this.emit('migrationFailed', {
        ...migrationInfo,
        error: error.message,
        rollbackSuccessful: false,
        rollbackError: rollbackError.message
      });
    }
  }

  /**
   * Get current session state
   * @returns {Object} Session state
   */
  getSessionState() {
    return {
      sessionId: this.sessionId,
      state: this.state,
      protocol: this.currentProtocol?.type || null,
      protocolVersion: this.currentProtocol?.version || null,
      participants: [...this.participants],
      isActive: this.state === 'ACTIVE',
      isMigrating: !!this.migrationState,
      migrationState: this.migrationState ? { ...this.migrationState } : null,
      capabilities: this.currentProtocol?.getCapabilities() || null,
      securityProperties: this.currentProtocol?.getSecurityProperties() || null,
      metrics: { ...this.metrics }
    };
  }

  /**
   * End the session
   */
  async endSession() {
    try {
      if (this.migrationTimeout) {
        clearTimeout(this.migrationTimeout);
        this.migrationTimeout = null;
      }

      if (this.currentProtocol && this.state === 'ACTIVE') {
        await this.currentProtocol.endSession();
      }

      this.setState('TERMINATED');
      
      this.metrics.sessionDuration = Date.now() - this.metrics.sessionStartTime;
      
      this.emit('sessionEnded', {
        sessionId: this.sessionId,
        duration: this.metrics.sessionDuration,
        metrics: this.metrics
      });

      await this.cleanup();

    } catch (error) {
      console.error('Error during session cleanup:', error);
      await this.cleanup();
    }
  }

  // Private helper methods

  /**
   * Build conversation context for protocol selection
   * @param {Array<string>} participants - Participants
   * @param {Object} options - Options
   * @returns {Object} Conversation context
   * @private
   */
  buildConversationContext(participants, options) {
    return {
      participantCount: participants.length,
      participants,
      existingProtocol: options.existingProtocol || null,
      userPreference: options.userPreference || null,
      capabilities: this.getParticipantCapabilities(),
      conversationType: options.conversationType || 'chat',
      securityRequirements: options.securityRequirements || {}
    };
  }

  /**
   * Create protocol handler
   * @param {string} protocolType - Protocol type
   * @param {Object} options - Protocol options
   * @returns {Promise<ProtocolInterface>} Protocol handler
   * @private
   */
  async createProtocolHandler(protocolType, options = {}) {
    switch (protocolType) {
      case 'OTR':
        return new OTRProtocol(options.otrVersion || 3, options.otrOptions);
      
      case 'MLS':
        return new MLSProtocol(options.mlsCipherSuite, options.mlsOptions);
      
      default:
        throw new ProtocolError(`Unknown protocol type: ${protocolType}`, 'UNKNOWN_PROTOCOL');
    }
  }

  /**
   * Set up protocol event handlers
   * @private
   */
  setupProtocolEventHandlers() {
    if (!this.currentProtocol) return;

    // Forward protocol events
    this.currentProtocol.on('stateChanged', (event) => {
      this.emit('protocolStateChanged', event);
    });

    this.currentProtocol.on('sessionEstablished', (event) => {
      this.emit('protocolSessionEstablished', event);
    });

    this.currentProtocol.on('participantVerified', (event) => {
      this.emit('participantVerified', event);
    });

    this.currentProtocol.on('protocolError', (event) => {
      this.emit('protocolError', event);
    });
  }

  /**
   * Add message to history
   * @param {Object} message - Message to add
   * @private
   */
  addToMessageHistory(message) {
    this.messageHistory.push(message);
    
    // Trim history if it exceeds max size
    if (this.messageHistory.length > this.maxHistorySize) {
      this.messageHistory = this.messageHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Set session state
   * @param {string} newState - New state
   * @private
   */
  setState(newState) {
    const oldState = this.state;
    this.state = newState;
    
    this.emit('stateChanged', {
      oldState,
      newState,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Generate session ID
   * @returns {string} Session ID
   * @private
   */
  generateSessionId() {
    return `unified-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get participant capabilities
   * @returns {Object} Capabilities
   * @private
   */
  getParticipantCapabilities() {
    // This would be implemented to query actual participant capabilities
    return {
      supportsOTR: true,
      supportsMLs: true // Assume MLS support for now
    };
  }

  /**
   * Clean up session resources
   * @private
   */
  async cleanup() {
    if (this.currentProtocol) {
      await this.currentProtocol.cleanup();
      this.currentProtocol = null;
    }

    this.protocolHandlers.clear();
    this.messageHistory = [];
    this.participants = [];
    this.conversationContext = null;
    this.removeAllListeners();
  }

  // Migration helper methods (placeholders for full implementation)
  async validateMigrationPrerequisites(toProtocol) { /* Implementation needed */ }
  async notifyParticipantsOfMigration(fromProtocol, toProtocol) { /* Implementation needed */ }
  async prepareNewProtocol(toProtocol) { /* Implementation needed */ }
  async exchangeKeys(toProtocol) { /* Implementation needed */ }
  async verifyNewProtocolSetup() { /* Implementation needed */ }
  async migrateConversationState(fromProtocol, toProtocol) { /* Implementation needed */ }
  async activateNewProtocol() { /* Implementation needed */ }
  async cleanupOldProtocol() { /* Implementation needed */ }
  async rollbackMigration() { /* Implementation needed */ }
}
