/**
 * MLS Cryptographic Operations
 * 
 * Implements the cryptographic primitives required for MLS protocol
 * including HPKE, key derivation, and credential management.
 */

/**
 * MLS Crypto engine providing cryptographic operations for MLS protocol
 */
export class MLSCrypto {
  /**
   * Create MLS crypto instance
   * @param {string} cipherSuite - MLS cipher suite identifier
   */
  constructor(cipherSuite) {
    this.cipherSuite = cipherSuite;
    this.suiteConfig = this.parseCipherSuite(cipherSuite);
    
    // Initialize crypto primitives based on cipher suite
    this.initializeCryptoPrimitives();
  }

  /**
   * Parse cipher suite identifier
   * @param {string} cipherSuite - Cipher suite string
   * @returns {Object} Parsed cipher suite configuration
   * @private
   */
  parseCipherSuite(cipherSuite) {
    // Parse standard MLS cipher suite format
    // Example: MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519
    const parts = cipherSuite.split('_');
    
    if (parts.length < 5 || parts[0] !== 'MLS') {
      throw new Error(`Invalid cipher suite format: ${cipherSuite}`);
    }

    return {
      securityLevel: parseInt(parts[1]),
      kem: parts[2],
      aead: parts[3],
      hash: parts[4],
      signature: parts[5] || parts[4] // Some suites combine hash and signature
    };
  }

  /**
   * Initialize crypto primitives based on cipher suite
   * @private
   */
  initializeCryptoPrimitives() {
    // Initialize based on cipher suite configuration
    switch (this.suiteConfig.kem) {
      case 'DHKEMX25519':
        this.kemAlgorithm = 'X25519';
        this.kemKeySize = 32;
        break;
      case 'DHKEMP256':
        this.kemAlgorithm = 'P-256';
        this.kemKeySize = 32;
        break;
      default:
        throw new Error(`Unsupported KEM: ${this.suiteConfig.kem}`);
    }

    switch (this.suiteConfig.aead) {
      case 'AES128GCM':
        this.aeadAlgorithm = 'AES-GCM';
        this.aeadKeySize = 16;
        break;
      case 'AES256GCM':
        this.aeadAlgorithm = 'AES-GCM';
        this.aeadKeySize = 32;
        break;
      default:
        throw new Error(`Unsupported AEAD: ${this.suiteConfig.aead}`);
    }

    switch (this.suiteConfig.hash) {
      case 'SHA256':
        this.hashAlgorithm = 'SHA-256';
        this.hashSize = 32;
        break;
      case 'SHA512':
        this.hashAlgorithm = 'SHA-512';
        this.hashSize = 64;
        break;
      default:
        throw new Error(`Unsupported hash: ${this.suiteConfig.hash}`);
    }
  }

  /**
   * Generate a key pair for the configured KEM
   * @returns {Promise<Object>} Key pair with public and private keys
   */
  async generateKeyPair() {
    try {
      const keyPair = await crypto.subtle.generateKey(
        {
          name: this.kemAlgorithm === 'X25519' ? 'X25519' : 'ECDH',
          namedCurve: this.kemAlgorithm === 'P-256' ? 'P-256' : undefined
        },
        true, // extractable
        ['deriveKey', 'deriveBits']
      );

      return {
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey
      };
    } catch (error) {
      throw new Error(`Failed to generate key pair: ${error.message}`);
    }
  }

  /**
   * Generate a KeyPackage for MLS
   * @param {string} identity - Identity of the key package owner
   * @returns {Promise<Object>} MLS KeyPackage
   */
  async generateKeyPackage(identity) {
    try {
      // Generate init key pair
      const initKeyPair = await this.generateKeyPair();
      
      // Create credential
      const credential = await this.createCredential(identity);
      
      // Create KeyPackage structure
      const keyPackage = {
        version: 1,
        cipherSuite: this.cipherSuite,
        initKey: await this.exportPublicKey(initKeyPair.publicKey),
        credential,
        extensions: [],
        signature: null // Will be filled after signing
      };

      // Sign the KeyPackage
      keyPackage.signature = await this.signKeyPackage(keyPackage, initKeyPair.privateKey);

      return keyPackage;
    } catch (error) {
      throw new Error(`Failed to generate KeyPackage: ${error.message}`);
    }
  }

  /**
   * Create a credential for identity
   * @param {string} identity - Identity string
   * @returns {Promise<Object>} MLS credential
   * @private
   */
  async createCredential(identity) {
    // Generate signature key pair for credential
    const signatureKeyPair = await this.generateSignatureKeyPair();
    
    return {
      credentialType: 'basic',
      identity: identity,
      publicKey: await this.exportPublicKey(signatureKeyPair.publicKey),
      privateKey: signatureKeyPair.privateKey // Keep private for signing
    };
  }

  /**
   * Generate signature key pair
   * @returns {Promise<Object>} Signature key pair
   * @private
   */
  async generateSignatureKeyPair() {
    const algorithm = this.suiteConfig.signature === 'Ed25519' ? 'Ed25519' : 'ECDSA';
    
    try {
      const keyPair = await crypto.subtle.generateKey(
        algorithm === 'Ed25519' ? 
          { name: 'Ed25519' } :
          { name: 'ECDSA', namedCurve: 'P-256' },
        true,
        ['sign', 'verify']
      );

      return keyPair;
    } catch (error) {
      throw new Error(`Failed to generate signature key pair: ${error.message}`);
    }
  }

  /**
   * Sign a KeyPackage
   * @param {Object} keyPackage - KeyPackage to sign
   * @param {CryptoKey} privateKey - Private key for signing
   * @returns {Promise<ArrayBuffer>} Signature
   * @private
   */
  async signKeyPackage(keyPackage, privateKey) {
    try {
      // Create KeyPackage TBS (To Be Signed) structure
      const tbs = this.createKeyPackageTBS(keyPackage);
      
      // Sign using credential private key
      const signature = await crypto.subtle.sign(
        this.getSignatureAlgorithm(),
        keyPackage.credential.privateKey,
        tbs
      );

      return signature;
    } catch (error) {
      throw new Error(`Failed to sign KeyPackage: ${error.message}`);
    }
  }

  /**
   * Verify a KeyPackage signature
   * @param {Object} keyPackage - KeyPackage to verify
   * @returns {Promise<boolean>} Verification result
   */
  async verifyKeyPackage(keyPackage) {
    try {
      const tbs = this.createKeyPackageTBS(keyPackage);
      
      const isValid = await crypto.subtle.verify(
        this.getSignatureAlgorithm(),
        keyPackage.credential.publicKey,
        keyPackage.signature,
        tbs
      );

      return isValid;
    } catch (error) {
      console.error('KeyPackage verification failed:', error);
      return false;
    }
  }

  /**
   * Verify a credential
   * @param {Object} credential - Credential to verify
   * @returns {Promise<boolean>} Verification result
   */
  async verifyCredential(credential) {
    // Basic credential verification
    // In a real implementation, this would check against a PKI or trust store
    return credential && 
           credential.credentialType === 'basic' && 
           credential.identity && 
           credential.publicKey;
  }

  /**
   * Derive secret using HKDF
   * @param {ArrayBuffer} inputKeyMaterial - Input key material
   * @param {string} label - HKDF label
   * @param {ArrayBuffer} context - HKDF context
   * @param {number} length - Output length in bytes
   * @returns {Promise<ArrayBuffer>} Derived secret
   */
  async deriveSecret(inputKeyMaterial, label, context, length) {
    try {
      // Import IKM as HKDF key
      const key = await crypto.subtle.importKey(
        'raw',
        inputKeyMaterial,
        'HKDF',
        false,
        ['deriveKey', 'deriveBits']
      );

      // Create info parameter (label + context)
      const info = this.createHKDFInfo(label, context);

      // Derive bits using HKDF
      const derivedBits = await crypto.subtle.deriveBits(
        {
          name: 'HKDF',
          hash: this.hashAlgorithm,
          salt: new ArrayBuffer(this.hashSize), // Zero salt
          info: info
        },
        key,
        length * 8 // Convert bytes to bits
      );

      return derivedBits;
    } catch (error) {
      throw new Error(`Failed to derive secret: ${error.message}`);
    }
  }

  /**
   * Encrypt data using AEAD
   * @param {ArrayBuffer} key - Encryption key
   * @param {ArrayBuffer} nonce - Nonce/IV
   * @param {ArrayBuffer} plaintext - Data to encrypt
   * @param {ArrayBuffer} additionalData - Additional authenticated data
   * @returns {Promise<ArrayBuffer>} Ciphertext
   */
  async encrypt(key, nonce, plaintext, additionalData = new ArrayBuffer(0)) {
    try {
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        key,
        { name: this.aeadAlgorithm },
        false,
        ['encrypt']
      );

      const ciphertext = await crypto.subtle.encrypt(
        {
          name: this.aeadAlgorithm,
          iv: nonce,
          additionalData: additionalData
        },
        cryptoKey,
        plaintext
      );

      return ciphertext;
    } catch (error) {
      throw new Error(`Failed to encrypt: ${error.message}`);
    }
  }

  /**
   * Decrypt data using AEAD
   * @param {ArrayBuffer} key - Decryption key
   * @param {ArrayBuffer} nonce - Nonce/IV
   * @param {ArrayBuffer} ciphertext - Data to decrypt
   * @param {ArrayBuffer} additionalData - Additional authenticated data
   * @returns {Promise<ArrayBuffer>} Plaintext
   */
  async decrypt(key, nonce, ciphertext, additionalData = new ArrayBuffer(0)) {
    try {
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        key,
        { name: this.aeadAlgorithm },
        false,
        ['decrypt']
      );

      const plaintext = await crypto.subtle.decrypt(
        {
          name: this.aeadAlgorithm,
          iv: nonce,
          additionalData: additionalData
        },
        cryptoKey,
        ciphertext
      );

      return plaintext;
    } catch (error) {
      throw new Error(`Failed to decrypt: ${error.message}`);
    }
  }

  /**
   * Compute hash
   * @param {ArrayBuffer} data - Data to hash
   * @returns {Promise<ArrayBuffer>} Hash value
   */
  async hash(data) {
    try {
      const hashValue = await crypto.subtle.digest(this.hashAlgorithm, data);
      return hashValue;
    } catch (error) {
      throw new Error(`Failed to compute hash: ${error.message}`);
    }
  }

  // Helper methods

  /**
   * Export public key to raw format
   * @param {CryptoKey} publicKey - Public key to export
   * @returns {Promise<ArrayBuffer>} Raw public key
   * @private
   */
  async exportPublicKey(publicKey) {
    try {
      return await crypto.subtle.exportKey('raw', publicKey);
    } catch (error) {
      throw new Error(`Failed to export public key: ${error.message}`);
    }
  }

  /**
   * Create KeyPackage TBS structure
   * @param {Object} keyPackage - KeyPackage
   * @returns {ArrayBuffer} TBS bytes
   * @private
   */
  createKeyPackageTBS(keyPackage) {
    // Create the "to be signed" structure for KeyPackage
    // This would normally use proper TLS serialization
    const encoder = new TextEncoder();
    const parts = [
      new Uint8Array([keyPackage.version]),
      encoder.encode(keyPackage.cipherSuite),
      new Uint8Array(keyPackage.initKey),
      encoder.encode(keyPackage.credential.identity)
    ];

    // Concatenate all parts
    const totalLength = parts.reduce((sum, part) => sum + part.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const part of parts) {
      result.set(part, offset);
      offset += part.length;
    }

    return result.buffer;
  }

  /**
   * Create HKDF info parameter
   * @param {string} label - HKDF label
   * @param {ArrayBuffer} context - HKDF context
   * @returns {ArrayBuffer} Info parameter
   * @private
   */
  createHKDFInfo(label, context) {
    const encoder = new TextEncoder();
    const labelBytes = encoder.encode(label);
    const contextBytes = new Uint8Array(context);
    
    const info = new Uint8Array(labelBytes.length + contextBytes.length);
    info.set(labelBytes, 0);
    info.set(contextBytes, labelBytes.length);
    
    return info.buffer;
  }

  /**
   * Get signature algorithm configuration
   * @returns {Object} Signature algorithm config
   * @private
   */
  getSignatureAlgorithm() {
    if (this.suiteConfig.signature === 'Ed25519') {
      return { name: 'Ed25519' };
    } else {
      return {
        name: 'ECDSA',
        hash: this.hashAlgorithm
      };
    }
  }

  /**
   * Generate random bytes
   * @param {number} length - Number of bytes to generate
   * @returns {ArrayBuffer} Random bytes
   */
  generateRandomBytes(length) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return array.buffer;
  }

  /**
   * Get cipher suite configuration
   * @returns {Object} Cipher suite configuration
   */
  getCipherSuiteConfig() {
    return {
      ...this.suiteConfig,
      kemKeySize: this.kemKeySize,
      aeadKeySize: this.aeadKeySize,
      hashSize: this.hashSize
    };
  }
}
