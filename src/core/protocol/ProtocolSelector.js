/**
 * Protocol Selection Engine
 * 
 * Intelligently selects between OTR and MLS protocols based on conversation
 * context, participant capabilities, and user preferences.
 */

import { ProtocolSelectionError, PROTOCOL_CAPABILITIES } from './ProtocolInterface.js';

/**
 * Protocol selection engine
 */
export class ProtocolSelector {
  /**
   * Create a protocol selector
   * @param {Object} policy - Selection policy configuration
   */
  constructor(policy = {}) {
    this.policy = {
      // Default policy settings
      preferOTRFor2Party: true,
      requireMLSForGroups: true,
      allowProtocolMigration: true,
      respectUserPreference: true,
      fallbackToCompatible: true,
      securityPriority: 'balanced', // 'privacy' | 'security' | 'balanced'
      
      // Override with provided policy
      ...policy
    };
  }

  /**
   * Select the appropriate protocol for a conversation
   * @param {Object} conversationContext - Conversation context
   * @returns {Object} Selection result with protocol and reasoning
   */
  selectProtocol(conversationContext) {
    const {
      participantCount,
      participants = [],
      existingProtocol = null,
      userPreference = null,
      capabilities = {},
      conversationType = 'chat', // 'chat' | 'meeting' | 'broadcast'
      securityRequirements = {}
    } = conversationContext;

    // Validate input
    this.validateConversationContext(conversationContext);

    // Determine available protocols based on capabilities
    const availableProtocols = this.getAvailableProtocols(capabilities);
    
    if (availableProtocols.length === 0) {
      throw new ProtocolSelectionError('No supported protocols available');
    }

    // Apply selection logic
    const selection = this.applySelectionLogic({
      participantCount,
      participants,
      existingProtocol,
      userPreference,
      availableProtocols,
      conversationType,
      securityRequirements
    });

    // Validate selection
    this.validateSelection(selection, conversationContext);

    return selection;
  }

  /**
   * Apply protocol selection logic
   * @param {Object} context - Selection context
   * @returns {Object} Selection result
   * @private
   */
  applySelectionLogic(context) {
    const {
      participantCount,
      existingProtocol,
      userPreference,
      availableProtocols,
      conversationType,
      securityRequirements
    } = context;

    // Rule 1: Group conversations require MLS
    if (participantCount >= 3) {
      if (availableProtocols.includes('MLS')) {
        return {
          protocol: 'MLS',
          reason: 'GROUP_CONVERSATION',
          confidence: 1.0,
          alternatives: []
        };
      } else {
        throw new ProtocolSelectionError(
          'MLS required for group conversations but not supported by all participants'
        );
      }
    }

    // Rule 2: Preserve existing protocol if possible
    if (existingProtocol && availableProtocols.includes(existingProtocol)) {
      return {
        protocol: existingProtocol,
        reason: 'EXISTING_SESSION',
        confidence: 0.9,
        alternatives: availableProtocols.filter(p => p !== existingProtocol)
      };
    }

    // Rule 3: Respect user preference if valid
    if (this.policy.respectUserPreference && userPreference) {
      if (availableProtocols.includes(userPreference)) {
        return {
          protocol: userPreference,
          reason: 'USER_PREFERENCE',
          confidence: 0.8,
          alternatives: availableProtocols.filter(p => p !== userPreference)
        };
      }
    }

    // Rule 4: Security requirements override
    if (securityRequirements.requireDeniability && availableProtocols.includes('OTR')) {
      return {
        protocol: 'OTR',
        reason: 'SECURITY_REQUIREMENT_DENIABILITY',
        confidence: 0.9,
        alternatives: []
      };
    }

    if (securityRequirements.requirePostCompromiseSecurity && availableProtocols.includes('MLS')) {
      return {
        protocol: 'MLS',
        reason: 'SECURITY_REQUIREMENT_PCS',
        confidence: 0.9,
        alternatives: []
      };
    }

    // Rule 5: Conversation type considerations
    if (conversationType === 'meeting' && availableProtocols.includes('MLS')) {
      return {
        protocol: 'MLS',
        reason: 'CONVERSATION_TYPE_MEETING',
        confidence: 0.7,
        alternatives: availableProtocols.filter(p => p !== 'MLS')
      };
    }

    // Rule 6: Default policy for two-party conversations
    if (participantCount === 2) {
      if (this.policy.preferOTRFor2Party && availableProtocols.includes('OTR')) {
        return {
          protocol: 'OTR',
          reason: 'POLICY_DEFAULT_2PARTY',
          confidence: 0.6,
          alternatives: availableProtocols.filter(p => p !== 'OTR')
        };
      }
    }

    // Rule 7: Security priority fallback
    const protocolBySecurityPriority = this.selectBySecurityPriority(availableProtocols);
    if (protocolBySecurityPriority) {
      return {
        protocol: protocolBySecurityPriority,
        reason: `SECURITY_PRIORITY_${this.policy.securityPriority.toUpperCase()}`,
        confidence: 0.5,
        alternatives: availableProtocols.filter(p => p !== protocolBySecurityPriority)
      };
    }

    // Rule 8: First available protocol (last resort)
    return {
      protocol: availableProtocols[0],
      reason: 'FALLBACK_FIRST_AVAILABLE',
      confidence: 0.3,
      alternatives: availableProtocols.slice(1)
    };
  }

  /**
   * Select protocol based on security priority
   * @param {Array<string>} availableProtocols - Available protocols
   * @returns {string|null} Selected protocol
   * @private
   */
  selectBySecurityPriority(availableProtocols) {
    switch (this.policy.securityPriority) {
      case 'privacy':
        // Prioritize deniability
        return availableProtocols.includes('OTR') ? 'OTR' : availableProtocols[0];
      
      case 'security':
        // Prioritize post-compromise security
        return availableProtocols.includes('MLS') ? 'MLS' : availableProtocols[0];
      
      case 'balanced':
      default:
        // Balanced approach - prefer newer protocol
        return availableProtocols.includes('MLS') ? 'MLS' : 
               availableProtocols.includes('OTR') ? 'OTR' : 
               availableProtocols[0];
    }
  }

  /**
   * Determine available protocols based on participant capabilities
   * @param {Object} capabilities - Participant capabilities
   * @returns {Array<string>} Available protocols
   * @private
   */
  getAvailableProtocols(capabilities) {
    const available = [];

    if (capabilities.supportsOTR !== false) {
      available.push('OTR');
    }

    if (capabilities.supportsMLs === true) {
      available.push('MLS');
    }

    return available;
  }

  /**
   * Check if protocol migration is possible
   * @param {string} fromProtocol - Current protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {Object} Migration feasibility result
   */
  canMigrate(fromProtocol, toProtocol, context) {
    const {
      participantCount,
      participants = [],
      capabilities = {},
      reason = 'USER_REQUEST'
    } = context;

    // Check policy allows migration
    if (!this.policy.allowProtocolMigration) {
      return {
        canMigrate: false,
        reason: 'POLICY_DISALLOWS_MIGRATION',
        requirements: []
      };
    }

    // Check protocol-specific migration rules
    const migrationRules = this.getMigrationRules(fromProtocol, toProtocol);
    
    if (!migrationRules.allowed) {
      return {
        canMigrate: false,
        reason: migrationRules.reason,
        requirements: []
      };
    }

    // Check participant count constraints
    const toProtocolCaps = PROTOCOL_CAPABILITIES[toProtocol];
    if (participantCount > toProtocolCaps.maxParticipants) {
      return {
        canMigrate: false,
        reason: 'PARTICIPANT_COUNT_EXCEEDS_LIMIT',
        requirements: [`Reduce participants to ${toProtocolCaps.maxParticipants} or fewer`]
      };
    }

    // Check capability requirements
    const capabilityCheck = this.checkMigrationCapabilities(toProtocol, capabilities);
    if (!capabilityCheck.satisfied) {
      return {
        canMigrate: false,
        reason: 'INSUFFICIENT_CAPABILITIES',
        requirements: capabilityCheck.requirements
      };
    }

    return {
      canMigrate: true,
      reason: 'MIGRATION_FEASIBLE',
      requirements: [],
      preservedProperties: migrationRules.preservedProperties,
      lostProperties: migrationRules.lostProperties
    };
  }

  /**
   * Get migration rules between protocols
   * @param {string} fromProtocol - Source protocol
   * @param {string} toProtocol - Target protocol
   * @returns {Object} Migration rules
   * @private
   */
  getMigrationRules(fromProtocol, toProtocol) {
    const rules = {
      'OTR->MLS': {
        allowed: true,
        preservedProperties: ['confidentiality', 'integrity', 'authenticity', 'forwardSecrecy'],
        lostProperties: ['deniability'],
        reason: 'UPGRADE_TO_GROUP_CAPABLE'
      },
      'MLS->OTR': {
        allowed: true,
        preservedProperties: ['confidentiality', 'integrity', 'authenticity', 'forwardSecrecy'],
        lostProperties: ['groupMessaging', 'postCompromiseSecurity'],
        reason: 'DOWNGRADE_FOR_PRIVACY'
      }
    };

    const key = `${fromProtocol}->${toProtocol}`;
    return rules[key] || {
      allowed: false,
      reason: 'UNSUPPORTED_MIGRATION_PATH'
    };
  }

  /**
   * Check if participants have required capabilities for migration
   * @param {string} toProtocol - Target protocol
   * @param {Object} capabilities - Participant capabilities
   * @returns {Object} Capability check result
   * @private
   */
  checkMigrationCapabilities(toProtocol, capabilities) {
    const requirements = [];
    let satisfied = true;

    if (toProtocol === 'MLS' && !capabilities.supportsMLs) {
      satisfied = false;
      requirements.push('All participants must support MLS protocol');
    }

    if (toProtocol === 'OTR' && capabilities.supportsOTR === false) {
      satisfied = false;
      requirements.push('All participants must support OTR protocol');
    }

    return { satisfied, requirements };
  }

  /**
   * Plan a protocol migration
   * @param {string} fromProtocol - Current protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {Object} Migration plan
   */
  planMigration(fromProtocol, toProtocol, context) {
    const feasibility = this.canMigrate(fromProtocol, toProtocol, context);
    
    if (!feasibility.canMigrate) {
      throw new ProtocolSelectionError(
        `Migration from ${fromProtocol} to ${toProtocol} not feasible: ${feasibility.reason}`
      );
    }

    return {
      fromProtocol,
      toProtocol,
      steps: this.generateMigrationSteps(fromProtocol, toProtocol, context),
      estimatedDuration: this.estimateMigrationDuration(fromProtocol, toProtocol, context),
      risks: this.assessMigrationRisks(fromProtocol, toProtocol, context),
      rollbackPlan: this.createRollbackPlan(fromProtocol, toProtocol, context)
    };
  }

  /**
   * Generate migration steps
   * @param {string} fromProtocol - Source protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {Array<Object>} Migration steps
   * @private
   */
  generateMigrationSteps(fromProtocol, toProtocol, context) {
    const baseSteps = [
      { step: 'VALIDATE_PREREQUISITES', description: 'Validate migration prerequisites' },
      { step: 'NOTIFY_PARTICIPANTS', description: 'Notify all participants of migration' },
      { step: 'PREPARE_NEW_PROTOCOL', description: `Initialize ${toProtocol} protocol` },
      { step: 'EXCHANGE_KEYS', description: 'Exchange cryptographic keys' },
      { step: 'VERIFY_SETUP', description: 'Verify new protocol setup' },
      { step: 'MIGRATE_STATE', description: 'Migrate conversation state' },
      { step: 'ACTIVATE_PROTOCOL', description: `Activate ${toProtocol} protocol` },
      { step: 'CLEANUP_OLD_PROTOCOL', description: `Clean up ${fromProtocol} protocol` }
    ];

    // Add protocol-specific steps
    if (toProtocol === 'MLS') {
      baseSteps.splice(3, 0, {
        step: 'CREATE_GROUP',
        description: 'Create MLS group and distribute Welcome messages'
      });
    }

    return baseSteps;
  }

  /**
   * Estimate migration duration
   * @param {string} fromProtocol - Source protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {number} Estimated duration in milliseconds
   * @private
   */
  estimateMigrationDuration(fromProtocol, toProtocol, context) {
    const baseTime = 2000; // 2 seconds base
    const perParticipantTime = 500; // 500ms per participant
    const protocolSetupTime = toProtocol === 'MLS' ? 1000 : 500;
    
    return baseTime + (context.participantCount * perParticipantTime) + protocolSetupTime;
  }

  /**
   * Assess migration risks
   * @param {string} fromProtocol - Source protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {Array<Object>} Risk assessment
   * @private
   */
  assessMigrationRisks(fromProtocol, toProtocol, context) {
    const risks = [];

    if (context.participantCount > 10) {
      risks.push({
        type: 'COORDINATION_COMPLEXITY',
        severity: 'medium',
        description: 'Large group migration may face coordination challenges'
      });
    }

    if (toProtocol === 'MLS') {
      risks.push({
        type: 'CAPABILITY_MISMATCH',
        severity: 'low',
        description: 'Some participants may have limited MLS support'
      });
    }

    return risks;
  }

  /**
   * Create rollback plan
   * @param {string} fromProtocol - Source protocol
   * @param {string} toProtocol - Target protocol
   * @param {Object} context - Migration context
   * @returns {Object} Rollback plan
   * @private
   */
  createRollbackPlan(fromProtocol, toProtocol, context) {
    return {
      triggerConditions: ['MIGRATION_TIMEOUT', 'PARTICIPANT_FAILURE', 'PROTOCOL_ERROR'],
      steps: [
        'HALT_MIGRATION',
        'RESTORE_ORIGINAL_PROTOCOL',
        'NOTIFY_PARTICIPANTS',
        'CLEANUP_PARTIAL_STATE'
      ],
      timeoutDuration: 30000 // 30 seconds
    };
  }

  /**
   * Validate conversation context
   * @param {Object} context - Conversation context
   * @throws {ProtocolSelectionError} If context is invalid
   * @private
   */
  validateConversationContext(context) {
    if (!context.participantCount || context.participantCount < 2) {
      throw new ProtocolSelectionError('Participant count must be at least 2');
    }

    if (context.participants && context.participants.length !== context.participantCount) {
      throw new ProtocolSelectionError('Participant count mismatch');
    }
  }

  /**
   * Validate protocol selection
   * @param {Object} selection - Protocol selection
   * @param {Object} context - Conversation context
   * @throws {ProtocolSelectionError} If selection is invalid
   * @private
   */
  validateSelection(selection, context) {
    const protocolCaps = PROTOCOL_CAPABILITIES[selection.protocol];
    
    if (!protocolCaps) {
      throw new ProtocolSelectionError(`Unknown protocol: ${selection.protocol}`);
    }

    if (context.participantCount > protocolCaps.maxParticipants) {
      throw new ProtocolSelectionError(
        `Protocol ${selection.protocol} cannot handle ${context.participantCount} participants`
      );
    }
  }
}
