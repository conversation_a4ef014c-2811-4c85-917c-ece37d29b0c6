/**
 * OTR Protocol Implementation
 * 
 * Concrete implementation of the OTR protocol that extends the unified
 * ProtocolInterface, integrating with existing WebOTR OTR functionality.
 */

import { ProtocolInterface, PROTOCOL_CAPABILITIES, ProtocolError } from './ProtocolInterface.js';
import { 
  OtrState, 
  STATE, 
  MESSAGE_TYPE, 
  PROTOCOL_VERSION,
  parseMessage,
  createQueryMessage,
  createDataMessage,
  createErrorMessage,
  encryptMessage,
  decryptMessage,
  startAKE,
  SMP,
  SMP_STATE
} from './index.js';
import { generateKeys, deriveKeys, generateInstanceTag } from '../crypto/index.js';

/**
 * OTR Protocol implementation extending the unified interface
 */
export class OTRProtocol extends ProtocolInterface {
  /**
   * Create an OTR protocol instance
   * @param {number} version - OTR protocol version (2 or 3)
   * @param {Object} options - Protocol options
   */
  constructor(version = PROTOCOL_VERSION.CURRENT, options = {}) {
    super('OTR', version, {
      ...PROTOCOL_CAPABILITIES.OTR,
      version
    });

    this.options = {
      policy: {
        allowV2: version >= 2,
        allowV3: version >= 3,
        requireEncryption: true,
        sendWhitespaceTag: true,
        whitespaceStartAKE: true,
        errorStartAKE: false
      },
      ...options
    };

    // OTR-specific state
    this.otrState = null;
    this.smpHandler = null;
    this.instanceTag = null;
    this.keyPair = null;
    this.fingerprint = null;
    this.remoteFingerprint = null;
    this.isVerified = false;
  }

  /**
   * Initialize OTR session
   * @param {Array<string>} participants - Participant identifiers
   * @param {Object} options - Session options
   */
  async initializeSession(participants, options = {}) {
    this.validateParticipants(participants);
    
    if (participants.length !== 2) {
      throw new ProtocolError('OTR only supports 2-party conversations', 'INVALID_PARTICIPANT_COUNT', this.type);
    }

    this.participants = [...participants];
    this.sessionId = this.generateSessionId();
    this.setState('INITIALIZING');

    try {
      // Generate instance tag and keys
      this.instanceTag = generateInstanceTag();
      this.keyPair = await generateKeys();
      this.fingerprint = this.generateFingerprint(this.keyPair.publicKey);

      // Initialize OTR state
      this.otrState = new OtrState(this.version);
      this.otrState.ourInstanceTag = this.instanceTag;
      this.otrState.dsaKeyPair = this.keyPair;

      // Initialize SMP handler
      this.smpHandler = new SMP(this.otrState);

      // Set up event handlers
      this.setupOTREventHandlers();

      this.setState('AWAITING_RESPONSE');
      this.emitProtocolEvent('sessionInitialized', {
        fingerprint: this.fingerprint,
        instanceTag: this.instanceTag
      });

    } catch (error) {
      this.setState('ERROR');
      throw new ProtocolError(`Failed to initialize OTR session: ${error.message}`, 'INITIALIZATION_FAILED', this.type);
    }
  }

  /**
   * Start OTR key exchange
   * @param {Object} options - AKE options
   */
  async startKeyExchange(options = {}) {
    if (this.state !== 'AWAITING_RESPONSE') {
      throw new ProtocolError('Invalid state for key exchange', 'INVALID_STATE', this.type);
    }

    try {
      this.setState('AUTHENTICATING');
      
      // Start AKE process
      const akeMessage = await startAKE(this.otrState, this.options.policy);
      
      this.emitProtocolEvent('keyExchangeStarted', {
        message: akeMessage
      });

      // Send the AKE message through transport
      await this.sendProtocolMessage(akeMessage);

    } catch (error) {
      this.setState('ERROR');
      throw new ProtocolError(`Key exchange failed: ${error.message}`, 'KEY_EXCHANGE_FAILED', this.type);
    }
  }

  /**
   * Send a message through OTR
   * @param {string} content - Message content
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Encrypted message
   */
  async sendMessage(content, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active for messaging', 'INVALID_STATE', this.type);
    }

    try {
      const encryptedMessage = await encryptMessage(content, this.otrState);
      
      const messageObject = {
        type: 'OTR_DATA',
        protocol: this.type,
        version: this.version,
        content: encryptedMessage,
        encrypted: true,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        instanceTag: this.instanceTag
      };

      this.emitProtocolEvent('messageSent', {
        originalContent: content,
        encryptedLength: encryptedMessage.length
      });

      return messageObject;

    } catch (error) {
      throw new ProtocolError(`Failed to encrypt message: ${error.message}`, 'ENCRYPTION_FAILED', this.type);
    }
  }

  /**
   * Receive and decrypt an OTR message
   * @param {Object} message - Encrypted message object
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Decrypted message result
   */
  async receiveMessage(message, context = {}) {
    try {
      // Parse the OTR message
      const parsedMessage = parseMessage(message.content);
      
      if (!parsedMessage) {
        throw new ProtocolError('Invalid OTR message format', 'INVALID_MESSAGE', this.type);
      }

      // Handle different message types
      switch (parsedMessage.type) {
        case MESSAGE_TYPE.QUERY:
          return await this.handleQueryMessage(parsedMessage, context);
        
        case MESSAGE_TYPE.DH_COMMIT:
        case MESSAGE_TYPE.DH_KEY:
        case MESSAGE_TYPE.REVEAL_SIGNATURE:
        case MESSAGE_TYPE.SIGNATURE:
          return await this.handleAKEMessage(parsedMessage, context);
        
        case MESSAGE_TYPE.DATA:
          return await this.handleDataMessage(parsedMessage, context);
        
        case MESSAGE_TYPE.ERROR:
          return await this.handleErrorMessage(parsedMessage, context);
        
        default:
          throw new ProtocolError(`Unknown message type: ${parsedMessage.type}`, 'UNKNOWN_MESSAGE_TYPE', this.type);
      }

    } catch (error) {
      this.emitProtocolEvent('messageReceiveError', {
        error: error.message,
        messageType: message.type
      });
      throw error;
    }
  }

  /**
   * Handle query message
   * @param {Object} parsedMessage - Parsed query message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Response
   * @private
   */
  async handleQueryMessage(parsedMessage, context) {
    if (this.state === 'UNINITIALIZED') {
      // Auto-initialize session on query
      await this.initializeSession([context.sender, context.recipient]);
    }

    // Start key exchange in response to query
    await this.startKeyExchange();

    return {
      type: 'QUERY_RESPONSE',
      action: 'KEY_EXCHANGE_STARTED',
      content: null
    };
  }

  /**
   * Handle AKE message
   * @param {Object} parsedMessage - Parsed AKE message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Response
   * @private
   */
  async handleAKEMessage(parsedMessage, context) {
    try {
      // Process AKE message through OTR state machine
      const response = await this.processAKEMessage(parsedMessage);
      
      if (this.otrState.state === STATE.ENCRYPTED) {
        this.setState('ACTIVE');
        this.remoteFingerprint = this.generateFingerprint(this.otrState.theirPublicKey);
        
        this.emitProtocolEvent('sessionEstablished', {
          remoteFingerprint: this.remoteFingerprint,
          isVerified: this.isVerified
        });
      }

      return {
        type: 'AKE_RESPONSE',
        content: response,
        sessionEstablished: this.state === 'ACTIVE'
      };

    } catch (error) {
      this.setState('ERROR');
      throw new ProtocolError(`AKE processing failed: ${error.message}`, 'AKE_FAILED', this.type);
    }
  }

  /**
   * Handle data message
   * @param {Object} parsedMessage - Parsed data message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Decrypted content
   * @private
   */
  async handleDataMessage(parsedMessage, context) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Cannot decrypt message: session not active', 'INVALID_STATE', this.type);
    }

    try {
      const decryptedContent = await decryptMessage(parsedMessage.content, this.otrState);
      
      this.emitProtocolEvent('messageReceived', {
        sender: context.sender,
        contentLength: decryptedContent.length
      });

      return {
        type: 'DECRYPTED_MESSAGE',
        content: decryptedContent,
        sender: context.sender,
        verified: this.isVerified,
        timestamp: context.timestamp || new Date().toISOString(),
        sessionId: this.sessionId
      };

    } catch (error) {
      throw new ProtocolError(`Failed to decrypt message: ${error.message}`, 'DECRYPTION_FAILED', this.type);
    }
  }

  /**
   * Handle error message
   * @param {Object} parsedMessage - Parsed error message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Error response
   * @private
   */
  async handleErrorMessage(parsedMessage, context) {
    this.emitProtocolEvent('protocolError', {
      error: parsedMessage.error,
      sender: context.sender
    });

    return {
      type: 'ERROR_RECEIVED',
      error: parsedMessage.error,
      action: 'SESSION_RESET_REQUIRED'
    };
  }

  /**
   * Add participant (not supported in OTR)
   * @param {string} participant - Participant identifier
   */
  async addParticipant(participant) {
    throw new ProtocolError('OTR does not support adding participants to existing sessions', 'UNSUPPORTED_OPERATION', this.type);
  }

  /**
   * Remove participant (not supported in OTR)
   * @param {string} participant - Participant identifier
   */
  async removeParticipant(participant) {
    throw new ProtocolError('OTR does not support removing participants from sessions', 'UNSUPPORTED_OPERATION', this.type);
  }

  /**
   * Verify participant using SMP or fingerprint
   * @param {string} participant - Participant identifier
   * @param {string} method - Verification method ('smp' or 'fingerprint')
   * @param {*} data - Verification data (secret for SMP, fingerprint for manual)
   * @returns {Promise<boolean>} Verification result
   */
  async verifyParticipant(participant, method = 'fingerprint', data = null) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Cannot verify participant: session not active', 'INVALID_STATE', this.type);
    }

    try {
      switch (method) {
        case 'smp':
          return await this.performSMPVerification(data);
        
        case 'fingerprint':
          return await this.performFingerprintVerification(data);
        
        default:
          throw new ProtocolError(`Unknown verification method: ${method}`, 'INVALID_VERIFICATION_METHOD', this.type);
      }

    } catch (error) {
      throw new ProtocolError(`Verification failed: ${error.message}`, 'VERIFICATION_FAILED', this.type);
    }
  }

  /**
   * Perform SMP verification
   * @param {string} secret - Shared secret
   * @returns {Promise<boolean>} Verification result
   * @private
   */
  async performSMPVerification(secret) {
    if (!secret) {
      throw new ProtocolError('Secret required for SMP verification', 'MISSING_SECRET', this.type);
    }

    return new Promise((resolve, reject) => {
      this.smpHandler.initiate(secret, (result) => {
        if (result === SMP.RESULT.SUCCESS) {
          this.isVerified = true;
          this.emitProtocolEvent('participantVerified', {
            method: 'smp',
            participant: this.participants.find(p => p !== this.sessionId)
          });
          resolve(true);
        } else {
          resolve(false);
        }
      });
    });
  }

  /**
   * Perform fingerprint verification
   * @param {string} expectedFingerprint - Expected remote fingerprint
   * @returns {Promise<boolean>} Verification result
   * @private
   */
  async performFingerprintVerification(expectedFingerprint) {
    if (!expectedFingerprint) {
      // Return current remote fingerprint for manual verification
      return {
        verified: false,
        remoteFingerprint: this.remoteFingerprint,
        action: 'MANUAL_VERIFICATION_REQUIRED'
      };
    }

    const verified = this.remoteFingerprint === expectedFingerprint.toUpperCase();
    
    if (verified) {
      this.isVerified = true;
      this.emitProtocolEvent('participantVerified', {
        method: 'fingerprint',
        participant: this.participants.find(p => p !== this.sessionId)
      });
    }

    return verified;
  }

  /**
   * End the OTR session
   */
  async endSession() {
    try {
      if (this.state === 'ACTIVE') {
        // Send disconnect message
        const disconnectMessage = createErrorMessage('User has disconnected');
        await this.sendProtocolMessage(disconnectMessage);
      }

      this.setState('TERMINATED');
      await this.cleanup();

      this.emitProtocolEvent('sessionEnded');

    } catch (error) {
      // Log error but don't throw - cleanup should always succeed
      console.error('Error during OTR session cleanup:', error);
      await this.cleanup();
    }
  }

  /**
   * Get OTR-specific performance characteristics
   * @returns {Object} Performance characteristics
   */
  getPerformanceCharacteristics() {
    return {
      ...super.getPerformanceCharacteristics(),
      setupLatency: 'medium', // AKE requires multiple round trips
      messageLatency: 'low',   // Direct encryption/decryption
      scalability: 'low',      // Limited to 2 participants
      memoryUsage: 'low',      // Minimal state
      cpuUsage: 'low'          // Efficient crypto operations
    };
  }

  // Private helper methods

  /**
   * Set up OTR-specific event handlers
   * @private
   */
  setupOTREventHandlers() {
    // Handle OTR state changes
    if (this.otrState) {
      this.otrState.on('stateChange', (newState) => {
        this.handleOTRStateChange(newState);
      });
    }

    // Handle SMP events
    if (this.smpHandler) {
      this.smpHandler.on('smpStateChange', (smpState) => {
        this.emitProtocolEvent('smpStateChanged', { smpState });
      });
    }
  }

  /**
   * Handle OTR state changes
   * @param {string} newState - New OTR state
   * @private
   */
  handleOTRStateChange(newState) {
    switch (newState) {
      case STATE.ENCRYPTED:
        this.setState('ACTIVE');
        break;
      case STATE.FINISHED:
        this.setState('TERMINATED');
        break;
      default:
        // Other states handled by specific methods
        break;
    }
  }

  /**
   * Process AKE message through OTR state machine
   * @param {Object} message - AKE message
   * @returns {Promise<string>} Response message
   * @private
   */
  async processAKEMessage(message) {
    // This would integrate with existing OTR AKE processing
    // Implementation depends on the specific OTR library structure
    throw new Error('AKE processing integration not yet implemented');
  }

  /**
   * Send protocol message through transport
   * @param {string} message - Protocol message
   * @private
   */
  async sendProtocolMessage(message) {
    this.emitProtocolEvent('protocolMessageSend', {
      message,
      type: 'PROTOCOL'
    });
  }

  /**
   * Generate fingerprint from public key
   * @param {Object} publicKey - Public key
   * @returns {string} Fingerprint
   * @private
   */
  generateFingerprint(publicKey) {
    // Implementation would generate SHA-1 hash of public key
    // This is a placeholder
    return 'PLACEHOLDER_FINGERPRINT';
  }

  /**
   * Clean up OTR-specific resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.smpHandler) {
      this.smpHandler.removeAllListeners();
      this.smpHandler = null;
    }

    if (this.otrState) {
      this.otrState.removeAllListeners();
      this.otrState = null;
    }

    this.keyPair = null;
    this.fingerprint = null;
    this.remoteFingerprint = null;
    this.isVerified = false;

    await super.cleanup();
  }
}
