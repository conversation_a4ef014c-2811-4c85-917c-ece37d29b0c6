/**
 * MLS Protocol Implementation
 * 
 * Concrete implementation of the MLS (Messaging Layer Security) protocol
 * that extends the unified ProtocolInterface, providing RFC 9420 compliant
 * group messaging capabilities.
 */

import { ProtocolInterface, PROTOCOL_CAPABILITIES, ProtocolError } from './ProtocolInterface.js';
import { MLSCrypto } from '../crypto/mls/MLSCrypto.js';
import { RatchetTree } from './mls/TreeKEM.js';
import { MLSStateMachine } from './mls/MLSStateMachine.js';
import { MLSMessageProcessor } from './mls/MLSMessageProcessor.js';

/**
 * MLS Protocol implementation extending the unified interface
 */
export class MLSProtocol extends ProtocolInterface {
  /**
   * Create an MLS protocol instance
   * @param {string} cipherSuite - MLS cipher suite identifier
   * @param {Object} options - Protocol options
   */
  constructor(cipherSuite = 'MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519', options = {}) {
    super('MLS', 1, {
      ...PROTOCOL_CAPABILITIES.MLS,
      cipherSuite
    });

    this.cipherSuite = cipherSuite;
    this.options = {
      maxGroupSize: 10000,
      keyUpdateInterval: 3600000, // 1 hour in milliseconds
      welcomeTimeout: 30000,      // 30 seconds
      proposalTimeout: 10000,     // 10 seconds
      ...options
    };

    // MLS-specific state
    this.groupId = null;
    this.epoch = 0;
    this.leafIndex = null;
    this.keyPackage = null;
    this.ratchetTree = null;
    this.stateMachine = null;
    this.messageProcessor = null;
    this.crypto = null;
    this.groupSecrets = new Map();
    this.pendingProposals = new Map();
    this.memberCredentials = new Map();
  }

  /**
   * Initialize MLS group session
   * @param {Array<string>} participants - Participant identifiers
   * @param {Object} options - Session options
   */
  async initializeSession(participants, options = {}) {
    this.validateParticipants(participants);
    
    if (participants.length < 2) {
      throw new ProtocolError('MLS requires at least 2 participants', 'INVALID_PARTICIPANT_COUNT', this.type);
    }

    if (participants.length > this.options.maxGroupSize) {
      throw new ProtocolError(`MLS group size limited to ${this.options.maxGroupSize} participants`, 'GROUP_SIZE_EXCEEDED', this.type);
    }

    this.participants = [...participants];
    this.sessionId = this.generateSessionId();
    this.groupId = this.generateGroupId();
    this.setState('INITIALIZING');

    try {
      // Initialize crypto engine
      this.crypto = new MLSCrypto(this.cipherSuite);
      
      // Generate our key package
      this.keyPackage = await this.crypto.generateKeyPackage(this.sessionId);
      
      // Initialize ratchet tree
      this.ratchetTree = new RatchetTree();
      
      // Initialize state machine
      this.stateMachine = new MLSStateMachine(this.groupId, this.epoch);
      
      // Initialize message processor
      this.messageProcessor = new MLSMessageProcessor(this.crypto, this.stateMachine);

      // Set up event handlers
      this.setupMLSEventHandlers();

      // Determine if we're creating or joining a group
      if (options.isCreator || participants[0] === this.sessionId) {
        await this.createGroup(participants, options);
      } else {
        this.setState('AWAITING_WELCOME');
      }

      this.emitProtocolEvent('sessionInitialized', {
        groupId: this.groupId,
        epoch: this.epoch,
        participantCount: participants.length,
        cipherSuite: this.cipherSuite
      });

    } catch (error) {
      this.setState('ERROR');
      throw new ProtocolError(`Failed to initialize MLS session: ${error.message}`, 'INITIALIZATION_FAILED', this.type);
    }
  }

  /**
   * Create a new MLS group
   * @param {Array<string>} participants - Initial participants
   * @param {Object} options - Creation options
   * @private
   */
  async createGroup(participants, options = {}) {
    try {
      this.setState('CREATING_GROUP');

      // Add ourselves to the tree first
      this.leafIndex = await this.ratchetTree.addLeaf(this.keyPackage);
      
      // Collect key packages from other participants
      const keyPackages = await this.collectKeyPackages(participants.filter(p => p !== this.sessionId));
      
      // Add other participants to the tree
      for (const keyPackage of keyPackages) {
        await this.ratchetTree.addLeaf(keyPackage);
        this.memberCredentials.set(keyPackage.credential.identity, keyPackage.credential);
      }

      // Initialize group secrets
      await this.initializeGroupSecrets();

      // Create and send Welcome messages
      await this.sendWelcomeMessages(keyPackages);

      this.setState('ACTIVE');
      this.emitProtocolEvent('groupCreated', {
        memberCount: this.ratchetTree.size,
        treeDepth: this.ratchetTree.depth
      });

    } catch (error) {
      throw new ProtocolError(`Failed to create MLS group: ${error.message}`, 'GROUP_CREATION_FAILED', this.type);
    }
  }

  /**
   * Send a message through MLS
   * @param {string} content - Message content
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Encrypted message
   */
  async sendMessage(content, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active for messaging', 'INVALID_STATE', this.type);
    }

    try {
      const applicationMessage = await this.messageProcessor.createApplicationMessage(
        content,
        this.leafIndex,
        this.epoch,
        this.groupSecrets.get(this.epoch)
      );

      const messageObject = {
        type: 'MLS_APPLICATION',
        protocol: this.type,
        version: this.version,
        content: applicationMessage,
        encrypted: true,
        timestamp: new Date().toISOString(),
        sessionId: this.sessionId,
        groupId: this.groupId,
        epoch: this.epoch,
        sender: this.leafIndex
      };

      this.emitProtocolEvent('messageSent', {
        originalContent: content,
        encryptedLength: applicationMessage.length,
        epoch: this.epoch
      });

      return messageObject;

    } catch (error) {
      throw new ProtocolError(`Failed to encrypt MLS message: ${error.message}`, 'ENCRYPTION_FAILED', this.type);
    }
  }

  /**
   * Receive and decrypt an MLS message
   * @param {Object} message - Encrypted message object
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Decrypted message result
   */
  async receiveMessage(message, context = {}) {
    try {
      // Validate message format
      if (!message.content || !message.groupId || message.epoch === undefined) {
        throw new ProtocolError('Invalid MLS message format', 'INVALID_MESSAGE', this.type);
      }

      // Check if message is for our group
      if (message.groupId !== this.groupId) {
        throw new ProtocolError('Message not for this group', 'WRONG_GROUP', this.type);
      }

      // Handle different message types
      switch (message.type) {
        case 'MLS_APPLICATION':
          return await this.handleApplicationMessage(message, context);
        
        case 'MLS_PROPOSAL':
          return await this.handleProposalMessage(message, context);
        
        case 'MLS_COMMIT':
          return await this.handleCommitMessage(message, context);
        
        case 'MLS_WELCOME':
          return await this.handleWelcomeMessage(message, context);
        
        default:
          throw new ProtocolError(`Unknown MLS message type: ${message.type}`, 'UNKNOWN_MESSAGE_TYPE', this.type);
      }

    } catch (error) {
      this.emitProtocolEvent('messageReceiveError', {
        error: error.message,
        messageType: message.type,
        epoch: message.epoch
      });
      throw error;
    }
  }

  /**
   * Handle application message
   * @param {Object} message - Application message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Decrypted content
   * @private
   */
  async handleApplicationMessage(message, context) {
    if (message.epoch !== this.epoch) {
      throw new ProtocolError(`Message epoch ${message.epoch} does not match current epoch ${this.epoch}`, 'EPOCH_MISMATCH', this.type);
    }

    try {
      const decryptedContent = await this.messageProcessor.decryptApplicationMessage(
        message.content,
        message.sender,
        this.epoch,
        this.groupSecrets.get(this.epoch)
      );

      this.emitProtocolEvent('messageReceived', {
        sender: message.sender,
        contentLength: decryptedContent.length,
        epoch: message.epoch
      });

      return {
        type: 'DECRYPTED_MESSAGE',
        content: decryptedContent,
        sender: this.getSenderIdentity(message.sender),
        verified: true, // MLS provides authentication
        timestamp: message.timestamp || new Date().toISOString(),
        sessionId: this.sessionId,
        groupId: this.groupId,
        epoch: message.epoch
      };

    } catch (error) {
      throw new ProtocolError(`Failed to decrypt application message: ${error.message}`, 'DECRYPTION_FAILED', this.type);
    }
  }

  /**
   * Handle proposal message
   * @param {Object} message - Proposal message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Proposal response
   * @private
   */
  async handleProposalMessage(message, context) {
    try {
      const proposal = await this.messageProcessor.processProposal(message.content);
      
      // Store pending proposal
      this.pendingProposals.set(proposal.id, proposal);

      this.emitProtocolEvent('proposalReceived', {
        proposalType: proposal.type,
        proposalId: proposal.id,
        sender: message.sender
      });

      return {
        type: 'PROPOSAL_RECEIVED',
        proposalType: proposal.type,
        proposalId: proposal.id,
        action: 'AWAITING_COMMIT'
      };

    } catch (error) {
      throw new ProtocolError(`Failed to process proposal: ${error.message}`, 'PROPOSAL_PROCESSING_FAILED', this.type);
    }
  }

  /**
   * Handle commit message
   * @param {Object} message - Commit message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Commit response
   * @private
   */
  async handleCommitMessage(message, context) {
    try {
      const commitResult = await this.stateMachine.processCommit(message.content);
      
      // Update our state based on commit
      await this.applyCommit(commitResult);

      this.emitProtocolEvent('commitProcessed', {
        newEpoch: this.epoch,
        changes: commitResult.changes
      });

      return {
        type: 'COMMIT_PROCESSED',
        newEpoch: this.epoch,
        changes: commitResult.changes
      };

    } catch (error) {
      throw new ProtocolError(`Failed to process commit: ${error.message}`, 'COMMIT_PROCESSING_FAILED', this.type);
    }
  }

  /**
   * Handle welcome message
   * @param {Object} message - Welcome message
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Welcome response
   * @private
   */
  async handleWelcomeMessage(message, context) {
    if (this.state !== 'AWAITING_WELCOME') {
      throw new ProtocolError('Not expecting Welcome message', 'UNEXPECTED_WELCOME', this.type);
    }

    try {
      const welcomeResult = await this.messageProcessor.processWelcome(message.content, this.keyPackage);
      
      // Initialize our state from Welcome
      await this.initializeFromWelcome(welcomeResult);

      this.setState('ACTIVE');
      this.emitProtocolEvent('groupJoined', {
        groupId: this.groupId,
        epoch: this.epoch,
        memberCount: this.participants.length
      });

      return {
        type: 'GROUP_JOINED',
        groupId: this.groupId,
        epoch: this.epoch
      };

    } catch (error) {
      throw new ProtocolError(`Failed to process Welcome: ${error.message}`, 'WELCOME_PROCESSING_FAILED', this.type);
    }
  }

  /**
   * Add a participant to the MLS group
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async addParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Cannot add participant: session not active', 'INVALID_STATE', this.type);
    }

    try {
      // Get key package for new participant
      const keyPackage = await this.getKeyPackage(participant);
      
      // Create Add proposal
      const addProposal = await this.stateMachine.createAddProposal(keyPackage);
      
      // Send proposal to group
      await this.sendProposal(addProposal);
      
      // Create and send commit
      const commit = await this.stateMachine.createCommit([addProposal.id]);
      await this.sendCommit(commit);

      // Update local state
      this.participants.push(participant);
      this.epoch++;

      this.emitProtocolEvent('participantAdded', {
        participant,
        newEpoch: this.epoch,
        memberCount: this.participants.length
      });

    } catch (error) {
      throw new ProtocolError(`Failed to add participant: ${error.message}`, 'ADD_PARTICIPANT_FAILED', this.type);
    }
  }

  /**
   * Remove a participant from the MLS group
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async removeParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Cannot remove participant: session not active', 'INVALID_STATE', this.type);
    }

    const participantIndex = this.participants.indexOf(participant);
    if (participantIndex === -1) {
      throw new ProtocolError('Participant not in group', 'PARTICIPANT_NOT_FOUND', this.type);
    }

    try {
      // Create Remove proposal
      const removeProposal = await this.stateMachine.createRemoveProposal(participantIndex);
      
      // Send proposal to group
      await this.sendProposal(removeProposal);
      
      // Create and send commit
      const commit = await this.stateMachine.createCommit([removeProposal.id]);
      await this.sendCommit(commit);

      // Update local state
      this.participants.splice(participantIndex, 1);
      this.epoch++;

      this.emitProtocolEvent('participantRemoved', {
        participant,
        newEpoch: this.epoch,
        memberCount: this.participants.length
      });

    } catch (error) {
      throw new ProtocolError(`Failed to remove participant: ${error.message}`, 'REMOVE_PARTICIPANT_FAILED', this.type);
    }
  }

  /**
   * Verify participant using MLS credentials
   * @param {string} participant - Participant identifier
   * @param {string} method - Verification method ('credential' or 'external')
   * @param {*} data - Verification data
   * @returns {Promise<boolean>} Verification result
   */
  async verifyParticipant(participant, method = 'credential', data = null) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Cannot verify participant: session not active', 'INVALID_STATE', this.type);
    }

    try {
      const credential = this.memberCredentials.get(participant);
      if (!credential) {
        throw new ProtocolError('Participant credential not found', 'CREDENTIAL_NOT_FOUND', this.type);
      }

      switch (method) {
        case 'credential':
          return await this.crypto.verifyCredential(credential);
        
        case 'external':
          return await this.performExternalVerification(participant, data);
        
        default:
          throw new ProtocolError(`Unknown verification method: ${method}`, 'INVALID_VERIFICATION_METHOD', this.type);
      }

    } catch (error) {
      throw new ProtocolError(`Verification failed: ${error.message}`, 'VERIFICATION_FAILED', this.type);
    }
  }

  /**
   * End the MLS session
   */
  async endSession() {
    try {
      if (this.state === 'ACTIVE') {
        // Send leave proposal if we're not the last member
        if (this.participants.length > 1) {
          const leaveProposal = await this.stateMachine.createRemoveProposal(this.leafIndex);
          await this.sendProposal(leaveProposal);
        }
      }

      this.setState('TERMINATED');
      await this.cleanup();

      this.emitProtocolEvent('sessionEnded');

    } catch (error) {
      // Log error but don't throw - cleanup should always succeed
      console.error('Error during MLS session cleanup:', error);
      await this.cleanup();
    }
  }

  /**
   * Get MLS-specific performance characteristics
   * @returns {Object} Performance characteristics
   */
  getPerformanceCharacteristics() {
    return {
      ...super.getPerformanceCharacteristics(),
      setupLatency: 'high',     // Group setup requires multiple round trips
      messageLatency: 'low',    // Direct encryption/decryption
      scalability: 'high',      // Supports large groups efficiently
      memoryUsage: 'medium',    // Tree state and group secrets
      cpuUsage: 'medium'        // TreeKEM operations
    };
  }

  // Private helper methods

  /**
   * Set up MLS-specific event handlers
   * @private
   */
  setupMLSEventHandlers() {
    if (this.stateMachine) {
      this.stateMachine.on('epochAdvanced', (newEpoch) => {
        this.epoch = newEpoch;
        this.emitProtocolEvent('epochAdvanced', { newEpoch });
      });
    }
  }

  /**
   * Generate a unique group ID
   * @returns {string} Group ID
   * @private
   */
  generateGroupId() {
    return `mls-group-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get sender identity from leaf index
   * @param {number} leafIndex - Sender's leaf index
   * @returns {string} Sender identity
   * @private
   */
  getSenderIdentity(leafIndex) {
    // This would map leaf index to participant identity
    return this.participants[leafIndex] || 'unknown';
  }

  /**
   * Clean up MLS-specific resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.stateMachine) {
      this.stateMachine.removeAllListeners();
      this.stateMachine = null;
    }

    this.messageProcessor = null;
    this.crypto = null;
    this.ratchetTree = null;
    this.groupSecrets.clear();
    this.pendingProposals.clear();
    this.memberCredentials.clear();

    await super.cleanup();
  }

  // Placeholder methods for integration points
  async collectKeyPackages(participants) { throw new Error('Not implemented'); }
  async initializeGroupSecrets() { throw new Error('Not implemented'); }
  async sendWelcomeMessages(keyPackages) { throw new Error('Not implemented'); }
  async applyCommit(commitResult) { throw new Error('Not implemented'); }
  async initializeFromWelcome(welcomeResult) { throw new Error('Not implemented'); }
  async getKeyPackage(participant) { throw new Error('Not implemented'); }
  async sendProposal(proposal) { throw new Error('Not implemented'); }
  async sendCommit(commit) { throw new Error('Not implemented'); }
  async performExternalVerification(participant, data) { throw new Error('Not implemented'); }
}
