/**
 * MLS Message Processor
 * 
 * Handles encryption, decryption, and processing of MLS application
 * messages, proposals, commits, and welcome messages.
 */

/**
 * MLS Message Processor for handling all MLS message types
 */
export class MLSMessageProcessor {
  /**
   * Create MLS message processor
   * @param {MLSCrypto} crypto - MLS crypto engine
   * @param {MLSStateMachine} stateMachine - MLS state machine
   */
  constructor(crypto, stateMachine) {
    this.crypto = crypto;
    this.stateMachine = stateMachine;
    
    // Message generation counters
    this.generationCounters = new Map();
    
    // Message ordering and replay protection
    this.receivedMessages = new Set();
    this.messageWindow = 1000; // Keep track of last 1000 messages
  }

  /**
   * Create an application message
   * @param {string} content - Message content
   * @param {number} senderIndex - Sender's leaf index
   * @param {number} epoch - Current epoch
   * @param {ArrayBuffer} groupSecret - Current group secret
   * @returns {Promise<ArrayBuffer>} Encrypted application message
   */
  async createApplicationMessage(content, senderIndex, epoch, groupSecret) {
    try {
      // Get or initialize generation counter for sender
      const generationKey = `${epoch}-${senderIndex}`;
      let generation = this.generationCounters.get(generationKey) || 0;
      
      // Derive application secret
      const applicationSecret = await this.deriveApplicationSecret(groupSecret, epoch);
      
      // Derive message key
      const messageKey = await this.deriveMessageKey(applicationSecret, senderIndex, generation);
      
      // Create message header
      const header = this.createApplicationMessageHeader(senderIndex, epoch, generation);
      
      // Encrypt content
      const nonce = await this.deriveNonce(messageKey, generation);
      const plaintext = new TextEncoder().encode(content);
      const ciphertext = await this.crypto.encrypt(messageKey, nonce, plaintext, header);
      
      // Create complete message
      const message = this.serializeApplicationMessage(header, ciphertext);
      
      // Increment generation counter
      this.generationCounters.set(generationKey, generation + 1);
      
      return message;
      
    } catch (error) {
      throw new Error(`Failed to create application message: ${error.message}`);
    }
  }

  /**
   * Decrypt an application message
   * @param {ArrayBuffer} messageData - Encrypted message data
   * @param {number} senderIndex - Sender's leaf index
   * @param {number} epoch - Message epoch
   * @param {ArrayBuffer} groupSecret - Group secret for the epoch
   * @returns {Promise<string>} Decrypted content
   */
  async decryptApplicationMessage(messageData, senderIndex, epoch, groupSecret) {
    try {
      // Parse message
      const { header, ciphertext } = this.parseApplicationMessage(messageData);
      
      // Validate message header
      this.validateApplicationMessageHeader(header, senderIndex, epoch);
      
      // Check for replay attacks
      const messageId = this.getMessageId(header);
      if (this.receivedMessages.has(messageId)) {
        throw new Error('Replay attack detected: message already received');
      }
      
      // Derive application secret
      const applicationSecret = await this.deriveApplicationSecret(groupSecret, epoch);
      
      // Derive message key
      const messageKey = await this.deriveMessageKey(applicationSecret, senderIndex, header.generation);
      
      // Decrypt content
      const nonce = await this.deriveNonce(messageKey, header.generation);
      const plaintext = await this.crypto.decrypt(messageKey, nonce, ciphertext, this.serializeHeader(header));
      
      // Mark message as received
      this.addReceivedMessage(messageId);
      
      // Decode content
      const content = new TextDecoder().decode(plaintext);
      
      return content;
      
    } catch (error) {
      throw new Error(`Failed to decrypt application message: ${error.message}`);
    }
  }

  /**
   * Process a proposal message
   * @param {ArrayBuffer} proposalData - Proposal message data
   * @returns {Promise<Object>} Processed proposal
   */
  async processProposal(proposalData) {
    try {
      // Parse proposal message
      const proposalMessage = this.parseProposalMessage(proposalData);
      
      // Validate proposal
      await this.validateProposalMessage(proposalMessage);
      
      // Extract proposal content
      const proposal = {
        id: proposalMessage.proposalId,
        type: proposalMessage.proposalType,
        epoch: proposalMessage.epoch,
        sender: proposalMessage.sender,
        content: proposalMessage.content,
        signature: proposalMessage.signature
      };
      
      return proposal;
      
    } catch (error) {
      throw new Error(`Failed to process proposal: ${error.message}`);
    }
  }

  /**
   * Process a commit message
   * @param {ArrayBuffer} commitData - Commit message data
   * @returns {Promise<Object>} Processed commit
   */
  async processCommit(commitData) {
    try {
      // Parse commit message
      const commitMessage = this.parseCommitMessage(commitData);
      
      // Validate commit
      await this.validateCommitMessage(commitMessage);
      
      // Extract commit content
      const commit = {
        id: commitMessage.commitId,
        epoch: commitMessage.epoch,
        sender: commitMessage.sender,
        proposals: commitMessage.proposals,
        path: commitMessage.path,
        confirmedTranscriptHash: commitMessage.confirmedTranscriptHash,
        signature: commitMessage.signature
      };
      
      return commit;
      
    } catch (error) {
      throw new Error(`Failed to process commit: ${error.message}`);
    }
  }

  /**
   * Process a welcome message
   * @param {ArrayBuffer} welcomeData - Welcome message data
   * @param {Object} keyPackage - Our key package
   * @returns {Promise<Object>} Welcome processing result
   */
  async processWelcome(welcomeData, keyPackage) {
    try {
      // Parse welcome message
      const welcomeMessage = this.parseWelcomeMessage(welcomeData);
      
      // Find our encrypted group info
      const encryptedGroupInfo = this.findEncryptedGroupInfo(welcomeMessage, keyPackage);
      if (!encryptedGroupInfo) {
        throw new Error('No encrypted group info found for our key package');
      }
      
      // Decrypt group info
      const groupInfo = await this.decryptGroupInfo(encryptedGroupInfo, keyPackage);
      
      // Validate group info
      await this.validateGroupInfo(groupInfo);
      
      // Extract group state
      const result = {
        groupId: groupInfo.groupId,
        epoch: groupInfo.epoch,
        ratchetTree: groupInfo.ratchetTree,
        confirmedTranscriptHash: groupInfo.confirmedTranscriptHash,
        groupSecrets: groupInfo.groupSecrets,
        memberIndex: this.findMemberIndex(groupInfo.ratchetTree, keyPackage)
      };
      
      return result;
      
    } catch (error) {
      throw new Error(`Failed to process welcome: ${error.message}`);
    }
  }

  // Private helper methods

  /**
   * Derive application secret from group secret
   * @param {ArrayBuffer} groupSecret - Group secret
   * @param {number} epoch - Epoch number
   * @returns {Promise<ArrayBuffer>} Application secret
   * @private
   */
  async deriveApplicationSecret(groupSecret, epoch) {
    const label = 'MLS 1.0 application';
    const context = new Uint8Array(4);
    new DataView(context.buffer).setUint32(0, epoch, false);
    
    return await this.crypto.deriveSecret(groupSecret, label, context.buffer, 32);
  }

  /**
   * Derive message key from application secret
   * @param {ArrayBuffer} applicationSecret - Application secret
   * @param {number} senderIndex - Sender index
   * @param {number} generation - Generation number
   * @returns {Promise<ArrayBuffer>} Message key
   * @private
   */
  async deriveMessageKey(applicationSecret, senderIndex, generation) {
    const label = 'MLS 1.0 key';
    const context = new Uint8Array(8);
    const view = new DataView(context.buffer);
    view.setUint32(0, senderIndex, false);
    view.setUint32(4, generation, false);
    
    return await this.crypto.deriveSecret(applicationSecret, label, context.buffer, 16);
  }

  /**
   * Derive nonce for encryption
   * @param {ArrayBuffer} messageKey - Message key
   * @param {number} generation - Generation number
   * @returns {Promise<ArrayBuffer>} Nonce
   * @private
   */
  async deriveNonce(messageKey, generation) {
    const label = 'MLS 1.0 nonce';
    const context = new Uint8Array(4);
    new DataView(context.buffer).setUint32(0, generation, false);
    
    const nonce = await this.crypto.deriveSecret(messageKey, label, context.buffer, 12);
    return nonce;
  }

  /**
   * Create application message header
   * @param {number} senderIndex - Sender index
   * @param {number} epoch - Epoch number
   * @param {number} generation - Generation number
   * @returns {Object} Message header
   * @private
   */
  createApplicationMessageHeader(senderIndex, epoch, generation) {
    return {
      messageType: 'APPLICATION',
      senderIndex,
      epoch,
      generation,
      timestamp: Date.now()
    };
  }

  /**
   * Serialize application message
   * @param {Object} header - Message header
   * @param {ArrayBuffer} ciphertext - Encrypted content
   * @returns {ArrayBuffer} Serialized message
   * @private
   */
  serializeApplicationMessage(header, ciphertext) {
    // Simple serialization for demo - real implementation would use proper encoding
    const headerBytes = this.serializeHeader(header);
    const result = new Uint8Array(headerBytes.byteLength + ciphertext.byteLength);
    result.set(new Uint8Array(headerBytes), 0);
    result.set(new Uint8Array(ciphertext), headerBytes.byteLength);
    
    return result.buffer;
  }

  /**
   * Parse application message
   * @param {ArrayBuffer} messageData - Message data
   * @returns {Object} Parsed message
   * @private
   */
  parseApplicationMessage(messageData) {
    // Simple parsing for demo - real implementation would use proper decoding
    const headerSize = 20; // Fixed size for demo
    const headerBytes = messageData.slice(0, headerSize);
    const ciphertext = messageData.slice(headerSize);
    
    const header = this.parseHeader(headerBytes);
    
    return { header, ciphertext };
  }

  /**
   * Serialize header
   * @param {Object} header - Header object
   * @returns {ArrayBuffer} Serialized header
   * @private
   */
  serializeHeader(header) {
    // Simple serialization for demo
    const buffer = new ArrayBuffer(20);
    const view = new DataView(buffer);
    
    view.setUint32(0, header.senderIndex, false);
    view.setUint32(4, header.epoch, false);
    view.setUint32(8, header.generation, false);
    view.setBigUint64(12, BigInt(header.timestamp), false);
    
    return buffer;
  }

  /**
   * Parse header
   * @param {ArrayBuffer} headerBytes - Header bytes
   * @returns {Object} Parsed header
   * @private
   */
  parseHeader(headerBytes) {
    const view = new DataView(headerBytes);
    
    return {
      messageType: 'APPLICATION',
      senderIndex: view.getUint32(0, false),
      epoch: view.getUint32(4, false),
      generation: view.getUint32(8, false),
      timestamp: Number(view.getBigUint64(12, false))
    };
  }

  /**
   * Validate application message header
   * @param {Object} header - Message header
   * @param {number} expectedSender - Expected sender index
   * @param {number} expectedEpoch - Expected epoch
   * @private
   */
  validateApplicationMessageHeader(header, expectedSender, expectedEpoch) {
    if (header.senderIndex !== expectedSender) {
      throw new Error(`Sender mismatch: expected ${expectedSender}, got ${header.senderIndex}`);
    }
    
    if (header.epoch !== expectedEpoch) {
      throw new Error(`Epoch mismatch: expected ${expectedEpoch}, got ${header.epoch}`);
    }
    
    // Check timestamp is reasonable (within last hour)
    const now = Date.now();
    const maxAge = 3600000; // 1 hour
    if (Math.abs(now - header.timestamp) > maxAge) {
      throw new Error('Message timestamp too old or too far in future');
    }
  }

  /**
   * Get unique message ID for replay protection
   * @param {Object} header - Message header
   * @returns {string} Message ID
   * @private
   */
  getMessageId(header) {
    return `${header.senderIndex}-${header.epoch}-${header.generation}`;
  }

  /**
   * Add message to received set for replay protection
   * @param {string} messageId - Message ID
   * @private
   */
  addReceivedMessage(messageId) {
    this.receivedMessages.add(messageId);
    
    // Trim received messages set if it gets too large
    if (this.receivedMessages.size > this.messageWindow) {
      const messagesToRemove = this.receivedMessages.size - this.messageWindow;
      const iterator = this.receivedMessages.values();
      
      for (let i = 0; i < messagesToRemove; i++) {
        const { value } = iterator.next();
        this.receivedMessages.delete(value);
      }
    }
  }

  // Placeholder methods for message parsing and validation

  parseProposalMessage(proposalData) {
    // Parse proposal from wire format
    return {}; // Placeholder
  }

  parseCommitMessage(commitData) {
    // Parse commit from wire format
    return {}; // Placeholder
  }

  parseWelcomeMessage(welcomeData) {
    // Parse welcome from wire format
    return {}; // Placeholder
  }

  async validateProposalMessage(proposalMessage) {
    // Validate proposal signature and content
    return true; // Placeholder
  }

  async validateCommitMessage(commitMessage) {
    // Validate commit signature and content
    return true; // Placeholder
  }

  findEncryptedGroupInfo(welcomeMessage, keyPackage) {
    // Find encrypted group info for our key package
    return {}; // Placeholder
  }

  async decryptGroupInfo(encryptedGroupInfo, keyPackage) {
    // Decrypt group info using our private key
    return {}; // Placeholder
  }

  async validateGroupInfo(groupInfo) {
    // Validate group info structure and signatures
    return true; // Placeholder
  }

  findMemberIndex(ratchetTree, keyPackage) {
    // Find our index in the ratchet tree
    return 0; // Placeholder
  }

  /**
   * Clean up resources
   */
  cleanup() {
    this.generationCounters.clear();
    this.receivedMessages.clear();
  }
}
