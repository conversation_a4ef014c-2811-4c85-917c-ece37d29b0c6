/**
 * MLS State Machine
 * 
 * Implements the MLS protocol state machine for handling proposals,
 * commits, and epoch transitions according to RFC 9420.
 */

import { EventEmitter } from 'events';

/**
 * MLS State Machine for managing group state and epoch transitions
 */
export class MLSStateMachine extends EventEmitter {
  /**
   * Create MLS state machine
   * @param {string} groupId - Group identifier
   * @param {number} initialEpoch - Initial epoch number
   */
  constructor(groupId, initialEpoch = 0) {
    super();
    
    this.groupId = groupId;
    this.epoch = initialEpoch;
    this.state = 'WAITING_FOR_WELCOME'; // Initial state
    
    // Proposal and commit tracking
    this.pendingProposals = new Map();
    this.proposalQueue = [];
    this.commitInProgress = null;
    
    // Group state
    this.groupContext = null;
    this.confirmedTranscriptHash = null;
    this.interimTranscriptHash = null;
    
    // Timers and timeouts
    this.proposalTimeout = 10000; // 10 seconds
    this.commitTimeout = 30000;   // 30 seconds
    this.proposalTimers = new Map();
    this.commitTimer = null;
  }

  /**
   * Initialize the state machine with group context
   * @param {Object} groupContext - Initial group context
   */
  initialize(groupContext) {
    this.groupContext = groupContext;
    this.state = 'ACTIVE';
    this.confirmedTranscriptHash = groupContext.confirmedTranscriptHash;
    this.interimTranscriptHash = groupContext.confirmedTranscriptHash;
    
    this.emit('initialized', {
      groupId: this.groupId,
      epoch: this.epoch,
      state: this.state
    });
  }

  /**
   * Create an Add proposal
   * @param {Object} keyPackage - Key package of member to add
   * @returns {Promise<Object>} Add proposal
   */
  async createAddProposal(keyPackage) {
    this.validateState(['ACTIVE']);
    
    const proposal = {
      id: this.generateProposalId(),
      type: 'ADD',
      epoch: this.epoch,
      sender: this.getSelfIndex(),
      keyPackage,
      timestamp: new Date().toISOString()
    };
    
    // Add to pending proposals
    this.pendingProposals.set(proposal.id, proposal);
    this.proposalQueue.push(proposal.id);
    
    // Set timeout for proposal
    this.setProposalTimeout(proposal.id);
    
    this.emit('proposalCreated', {
      proposalId: proposal.id,
      type: 'ADD',
      keyPackage
    });
    
    return proposal;
  }

  /**
   * Create a Remove proposal
   * @param {number} memberIndex - Index of member to remove
   * @returns {Promise<Object>} Remove proposal
   */
  async createRemoveProposal(memberIndex) {
    this.validateState(['ACTIVE']);
    
    const proposal = {
      id: this.generateProposalId(),
      type: 'REMOVE',
      epoch: this.epoch,
      sender: this.getSelfIndex(),
      memberIndex,
      timestamp: new Date().toISOString()
    };
    
    // Add to pending proposals
    this.pendingProposals.set(proposal.id, proposal);
    this.proposalQueue.push(proposal.id);
    
    // Set timeout for proposal
    this.setProposalTimeout(proposal.id);
    
    this.emit('proposalCreated', {
      proposalId: proposal.id,
      type: 'REMOVE',
      memberIndex
    });
    
    return proposal;
  }

  /**
   * Create an Update proposal
   * @param {Object} newKeyPackage - New key package for update
   * @returns {Promise<Object>} Update proposal
   */
  async createUpdateProposal(newKeyPackage) {
    this.validateState(['ACTIVE']);
    
    const proposal = {
      id: this.generateProposalId(),
      type: 'UPDATE',
      epoch: this.epoch,
      sender: this.getSelfIndex(),
      keyPackage: newKeyPackage,
      timestamp: new Date().toISOString()
    };
    
    // Add to pending proposals
    this.pendingProposals.set(proposal.id, proposal);
    this.proposalQueue.push(proposal.id);
    
    // Set timeout for proposal
    this.setProposalTimeout(proposal.id);
    
    this.emit('proposalCreated', {
      proposalId: proposal.id,
      type: 'UPDATE',
      keyPackage: newKeyPackage
    });
    
    return proposal;
  }

  /**
   * Process an incoming proposal
   * @param {Object} proposalMessage - Proposal message
   * @returns {Promise<Object>} Processing result
   */
  async processProposal(proposalMessage) {
    this.validateState(['ACTIVE']);
    
    try {
      // Validate proposal
      await this.validateProposal(proposalMessage);
      
      // Extract proposal from message
      const proposal = this.extractProposal(proposalMessage);
      
      // Check for duplicate proposals
      if (this.pendingProposals.has(proposal.id)) {
        throw new Error(`Duplicate proposal: ${proposal.id}`);
      }
      
      // Add to pending proposals
      this.pendingProposals.set(proposal.id, proposal);
      this.proposalQueue.push(proposal.id);
      
      // Set timeout for proposal
      this.setProposalTimeout(proposal.id);
      
      // Update interim transcript hash
      await this.updateInterimTranscriptHash(proposalMessage);
      
      this.emit('proposalReceived', {
        proposalId: proposal.id,
        type: proposal.type,
        sender: proposal.sender
      });
      
      return {
        proposalId: proposal.id,
        accepted: true,
        reason: 'PROPOSAL_VALID'
      };
      
    } catch (error) {
      this.emit('proposalRejected', {
        error: error.message,
        proposalMessage
      });
      
      return {
        accepted: false,
        reason: error.message
      };
    }
  }

  /**
   * Create a Commit message
   * @param {Array<string>} proposalIds - IDs of proposals to commit
   * @returns {Promise<Object>} Commit message
   */
  async createCommit(proposalIds) {
    this.validateState(['ACTIVE']);
    
    if (this.commitInProgress) {
      throw new Error('Commit already in progress');
    }
    
    // Validate all proposal IDs exist
    for (const proposalId of proposalIds) {
      if (!this.pendingProposals.has(proposalId)) {
        throw new Error(`Unknown proposal: ${proposalId}`);
      }
    }
    
    try {
      this.state = 'COMMITTING';
      this.commitInProgress = {
        proposalIds,
        startTime: Date.now()
      };
      
      // Set commit timeout
      this.setCommitTimeout();
      
      const commit = {
        id: this.generateCommitId(),
        epoch: this.epoch,
        sender: this.getSelfIndex(),
        proposals: proposalIds,
        path: await this.generateCommitPath(proposalIds),
        confirmedTranscriptHash: this.confirmedTranscriptHash,
        timestamp: new Date().toISOString()
      };
      
      this.emit('commitCreated', {
        commitId: commit.id,
        proposalIds,
        newEpoch: this.epoch + 1
      });
      
      return commit;
      
    } catch (error) {
      this.state = 'ACTIVE';
      this.commitInProgress = null;
      this.clearCommitTimeout();
      throw error;
    }
  }

  /**
   * Process an incoming Commit message
   * @param {Object} commitMessage - Commit message
   * @returns {Promise<Object>} Processing result
   */
  async processCommit(commitMessage) {
    this.validateState(['ACTIVE', 'COMMITTING']);
    
    try {
      // Validate commit
      await this.validateCommit(commitMessage);
      
      // Extract commit from message
      const commit = this.extractCommit(commitMessage);
      
      // Apply the commit
      const result = await this.applyCommit(commit);
      
      this.emit('commitProcessed', {
        commitId: commit.id,
        oldEpoch: this.epoch,
        newEpoch: this.epoch + 1,
        changes: result.changes
      });
      
      return result;
      
    } catch (error) {
      this.emit('commitRejected', {
        error: error.message,
        commitMessage
      });
      
      throw error;
    }
  }

  /**
   * Apply a commit and advance to the next epoch
   * @param {Object} commit - Commit to apply
   * @returns {Promise<Object>} Application result
   * @private
   */
  async applyCommit(commit) {
    const changes = {
      added: [],
      removed: [],
      updated: []
    };
    
    // Apply each proposal in the commit
    for (const proposalId of commit.proposals) {
      const proposal = this.pendingProposals.get(proposalId);
      if (!proposal) {
        throw new Error(`Proposal not found: ${proposalId}`);
      }
      
      switch (proposal.type) {
        case 'ADD':
          changes.added.push(proposal.keyPackage);
          break;
        case 'REMOVE':
          changes.removed.push(proposal.memberIndex);
          break;
        case 'UPDATE':
          changes.updated.push({
            memberIndex: proposal.sender,
            keyPackage: proposal.keyPackage
          });
          break;
      }
      
      // Remove from pending proposals
      this.pendingProposals.delete(proposalId);
      this.clearProposalTimeout(proposalId);
    }
    
    // Remove processed proposals from queue
    this.proposalQueue = this.proposalQueue.filter(id => !commit.proposals.includes(id));
    
    // Advance epoch
    this.epoch++;
    
    // Update transcript hashes
    this.confirmedTranscriptHash = await this.computeConfirmedTranscriptHash(commit);
    this.interimTranscriptHash = this.confirmedTranscriptHash;
    
    // Reset state
    this.state = 'ACTIVE';
    this.commitInProgress = null;
    this.clearCommitTimeout();
    
    this.emit('epochAdvanced', {
      newEpoch: this.epoch,
      changes
    });
    
    return {
      newEpoch: this.epoch,
      changes,
      confirmedTranscriptHash: this.confirmedTranscriptHash
    };
  }

  /**
   * Get current state information
   * @returns {Object} State information
   */
  getState() {
    return {
      groupId: this.groupId,
      epoch: this.epoch,
      state: this.state,
      pendingProposals: this.pendingProposals.size,
      proposalQueue: [...this.proposalQueue],
      commitInProgress: !!this.commitInProgress,
      confirmedTranscriptHash: this.confirmedTranscriptHash,
      interimTranscriptHash: this.interimTranscriptHash
    };
  }

  /**
   * Clear all pending proposals
   */
  clearPendingProposals() {
    // Clear all proposal timers
    for (const proposalId of this.pendingProposals.keys()) {
      this.clearProposalTimeout(proposalId);
    }
    
    this.pendingProposals.clear();
    this.proposalQueue = [];
    
    this.emit('proposalsCleared');
  }

  /**
   * Reset the state machine
   */
  reset() {
    this.clearPendingProposals();
    this.clearCommitTimeout();
    
    this.state = 'WAITING_FOR_WELCOME';
    this.commitInProgress = null;
    this.groupContext = null;
    this.confirmedTranscriptHash = null;
    this.interimTranscriptHash = null;
    
    this.emit('reset');
  }

  // Private helper methods

  /**
   * Validate current state
   * @param {Array<string>} validStates - Valid states
   * @private
   */
  validateState(validStates) {
    if (!validStates.includes(this.state)) {
      throw new Error(`Invalid state: ${this.state}. Expected one of: ${validStates.join(', ')}`);
    }
  }

  /**
   * Generate a unique proposal ID
   * @returns {string} Proposal ID
   * @private
   */
  generateProposalId() {
    return `prop-${this.epoch}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a unique commit ID
   * @returns {string} Commit ID
   * @private
   */
  generateCommitId() {
    return `commit-${this.epoch}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get self member index
   * @returns {number} Self index
   * @private
   */
  getSelfIndex() {
    // This would be determined during group initialization
    return 0; // Placeholder
  }

  /**
   * Set timeout for a proposal
   * @param {string} proposalId - Proposal ID
   * @private
   */
  setProposalTimeout(proposalId) {
    const timer = setTimeout(() => {
      this.handleProposalTimeout(proposalId);
    }, this.proposalTimeout);
    
    this.proposalTimers.set(proposalId, timer);
  }

  /**
   * Clear timeout for a proposal
   * @param {string} proposalId - Proposal ID
   * @private
   */
  clearProposalTimeout(proposalId) {
    const timer = this.proposalTimers.get(proposalId);
    if (timer) {
      clearTimeout(timer);
      this.proposalTimers.delete(proposalId);
    }
  }

  /**
   * Handle proposal timeout
   * @param {string} proposalId - Proposal ID
   * @private
   */
  handleProposalTimeout(proposalId) {
    this.pendingProposals.delete(proposalId);
    this.proposalQueue = this.proposalQueue.filter(id => id !== proposalId);
    
    this.emit('proposalTimeout', { proposalId });
  }

  /**
   * Set commit timeout
   * @private
   */
  setCommitTimeout() {
    this.commitTimer = setTimeout(() => {
      this.handleCommitTimeout();
    }, this.commitTimeout);
  }

  /**
   * Clear commit timeout
   * @private
   */
  clearCommitTimeout() {
    if (this.commitTimer) {
      clearTimeout(this.commitTimer);
      this.commitTimer = null;
    }
  }

  /**
   * Handle commit timeout
   * @private
   */
  handleCommitTimeout() {
    const commitInfo = { ...this.commitInProgress };
    
    this.state = 'ACTIVE';
    this.commitInProgress = null;
    
    this.emit('commitTimeout', { commitInfo });
  }

  // Placeholder methods for cryptographic operations

  async validateProposal(proposalMessage) {
    // Validate proposal signature, epoch, etc.
    return true;
  }

  async validateCommit(commitMessage) {
    // Validate commit signature, proposals, etc.
    return true;
  }

  extractProposal(proposalMessage) {
    // Extract proposal from wire format
    return proposalMessage; // Placeholder
  }

  extractCommit(commitMessage) {
    // Extract commit from wire format
    return commitMessage; // Placeholder
  }

  async generateCommitPath(proposalIds) {
    // Generate TreeKEM path for commit
    return []; // Placeholder
  }

  async updateInterimTranscriptHash(message) {
    // Update interim transcript hash with new message
    // Placeholder implementation
  }

  async computeConfirmedTranscriptHash(commit) {
    // Compute new confirmed transcript hash
    return new ArrayBuffer(32); // Placeholder
  }
}
