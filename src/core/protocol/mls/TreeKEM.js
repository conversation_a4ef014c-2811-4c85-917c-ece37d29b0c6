/**
 * TreeKEM Implementation for MLS
 * 
 * Implements the binary tree-based key management system used in MLS
 * for efficient group key updates and member management.
 */

import { EventEmitter } from 'events';

/**
 * Ratchet Tree implementation for MLS TreeKEM
 */
export class RatchetTree extends EventEmitter {
  /**
   * Create a new ratchet tree
   */
  constructor() {
    super();
    this.nodes = [];
    this.size = 0; // Number of leaf nodes
    this.depth = 0;
  }

  // Tree math operations

  /**
   * Get parent index of a node
   * @param {number} index - Node index
   * @returns {number} Parent index
   */
  parent(index) {
    if (index === 0) return null;
    return Math.floor((index - 1) / 2);
  }

  /**
   * Get left child index of a node
   * @param {number} index - Node index
   * @returns {number} Left child index
   */
  leftChild(index) {
    return 2 * index + 1;
  }

  /**
   * Get right child index of a node
   * @param {number} index - Node index
   * @returns {number} Right child index
   */
  rightChild(index) {
    return 2 * index + 2;
  }

  /**
   * Get sibling index of a node
   * @param {number} index - Node index
   * @returns {number} Sibling index
   */
  sibling(index) {
    if (index === 0) return null;
    return index % 2 === 0 ? index - 1 : index + 1;
  }

  /**
   * Check if a node is a leaf
   * @param {number} index - Node index
   * @returns {boolean} True if leaf node
   */
  isLeaf(index) {
    return this.leftChild(index) >= this.nodes.length;
  }

  /**
   * Check if a node is the root
   * @param {number} index - Node index
   * @returns {boolean} True if root node
   */
  isRoot(index) {
    return index === 0;
  }

  /**
   * Get the level of a node in the tree
   * @param {number} index - Node index
   * @returns {number} Node level (0 for root)
   */
  level(index) {
    if (index === 0) return 0;
    return Math.floor(Math.log2(index + 1));
  }

  /**
   * Get the path from a leaf to the root
   * @param {number} leafIndex - Leaf node index
   * @returns {Array<number>} Path indices from leaf to root
   */
  pathToRoot(leafIndex) {
    const path = [];
    let currentIndex = leafIndex;
    
    while (currentIndex !== null) {
      path.push(currentIndex);
      currentIndex = this.parent(currentIndex);
    }
    
    return path;
  }

  /**
   * Get the copath (sibling nodes) for a path
   * @param {Array<number>} path - Path indices
   * @returns {Array<number>} Copath indices
   */
  copath(path) {
    const copath = [];
    
    for (const nodeIndex of path) {
      const siblingIndex = this.sibling(nodeIndex);
      if (siblingIndex !== null && siblingIndex < this.nodes.length) {
        copath.push(siblingIndex);
      }
    }
    
    return copath;
  }

  // Tree operations

  /**
   * Add a leaf node to the tree
   * @param {Object} keyPackage - Key package for the new leaf
   * @returns {Promise<number>} Index of the added leaf
   */
  async addLeaf(keyPackage) {
    const leafIndex = this.findNextLeafIndex();
    
    // Create leaf node
    const leafNode = {
      type: 'leaf',
      keyPackage,
      privateKey: null, // Will be set if this is our leaf
      publicKey: keyPackage.initKey,
      credential: keyPackage.credential
    };
    
    // Ensure tree is large enough
    this.ensureTreeSize(leafIndex + 1);
    
    // Add the leaf
    this.nodes[leafIndex] = leafNode;
    this.size++;
    
    // Update tree depth
    this.updateDepth();
    
    // Update path to root
    await this.updatePathToRoot(leafIndex);
    
    this.emit('leafAdded', { leafIndex, keyPackage });
    
    return leafIndex;
  }

  /**
   * Remove a leaf node from the tree
   * @param {number} leafIndex - Index of leaf to remove
   * @returns {Promise<void>}
   */
  async removeLeaf(leafIndex) {
    if (!this.nodes[leafIndex] || this.nodes[leafIndex].type !== 'leaf') {
      throw new Error(`No leaf at index ${leafIndex}`);
    }
    
    // Mark leaf as blank
    this.nodes[leafIndex] = { type: 'blank' };
    this.size--;
    
    // Update path to root
    await this.updatePathToRoot(leafIndex);
    
    this.emit('leafRemoved', { leafIndex });
  }

  /**
   * Update a leaf node's key package
   * @param {number} leafIndex - Index of leaf to update
   * @param {Object} newKeyPackage - New key package
   * @returns {Promise<void>}
   */
  async updateLeaf(leafIndex, newKeyPackage) {
    if (!this.nodes[leafIndex] || this.nodes[leafIndex].type !== 'leaf') {
      throw new Error(`No leaf at index ${leafIndex}`);
    }
    
    // Update leaf node
    this.nodes[leafIndex].keyPackage = newKeyPackage;
    this.nodes[leafIndex].publicKey = newKeyPackage.initKey;
    this.nodes[leafIndex].credential = newKeyPackage.credential;
    
    // Update path to root
    await this.updatePathToRoot(leafIndex);
    
    this.emit('leafUpdated', { leafIndex, newKeyPackage });
  }

  /**
   * Update the path from a leaf to the root
   * @param {number} leafIndex - Starting leaf index
   * @returns {Promise<void>}
   */
  async updatePathToRoot(leafIndex) {
    const path = this.pathToRoot(leafIndex);
    
    // Update each node in the path (except the leaf itself)
    for (let i = 1; i < path.length; i++) {
      const nodeIndex = path[i];
      await this.updateParentNode(nodeIndex);
    }
    
    this.emit('pathUpdated', { leafIndex, path });
  }

  /**
   * Update a parent node based on its children
   * @param {number} nodeIndex - Index of parent node to update
   * @returns {Promise<void>}
   * @private
   */
  async updateParentNode(nodeIndex) {
    const leftChildIndex = this.leftChild(nodeIndex);
    const rightChildIndex = this.rightChild(nodeIndex);
    
    const leftChild = this.nodes[leftChildIndex];
    const rightChild = this.nodes[rightChildIndex];
    
    // Skip if children don't exist or are blank
    if (!leftChild || !rightChild || 
        leftChild.type === 'blank' || rightChild.type === 'blank') {
      this.nodes[nodeIndex] = { type: 'blank' };
      return;
    }
    
    // Create parent node by combining children
    const parentNode = {
      type: 'parent',
      publicKey: await this.combinePublicKeys(leftChild.publicKey, rightChild.publicKey),
      leftChild: leftChildIndex,
      rightChild: rightChildIndex,
      pathSecret: null // Will be computed during key derivation
    };
    
    this.nodes[nodeIndex] = parentNode;
  }

  /**
   * Compute path secrets for a given leaf
   * @param {number} leafIndex - Leaf index
   * @returns {Promise<Array<ArrayBuffer>>} Path secrets from leaf to root
   */
  async computePathSecrets(leafIndex) {
    const path = this.pathToRoot(leafIndex);
    const pathSecrets = [];
    
    // Generate random secret for the leaf
    let currentSecret = this.generateRandomSecret();
    
    // Derive secrets up the path
    for (let i = 1; i < path.length; i++) {
      const nodeIndex = path[i];
      
      // Derive next secret
      currentSecret = await this.derivePathSecret(currentSecret, nodeIndex);
      pathSecrets.push(currentSecret);
      
      // Store in node
      if (this.nodes[nodeIndex]) {
        this.nodes[nodeIndex].pathSecret = currentSecret;
      }
    }
    
    return pathSecrets;
  }

  /**
   * Get the tree hash (root hash of the tree)
   * @returns {Promise<ArrayBuffer>} Tree hash
   */
  async computeTreeHash() {
    if (this.nodes.length === 0) {
      return new ArrayBuffer(32); // Empty hash
    }
    
    return await this.computeNodeHash(0);
  }

  /**
   * Compute hash for a specific node
   * @param {number} nodeIndex - Node index
   * @returns {Promise<ArrayBuffer>} Node hash
   * @private
   */
  async computeNodeHash(nodeIndex) {
    const node = this.nodes[nodeIndex];
    
    if (!node || node.type === 'blank') {
      return new ArrayBuffer(32); // Blank node hash
    }
    
    if (node.type === 'leaf') {
      // Hash the key package
      return await this.hashKeyPackage(node.keyPackage);
    } else {
      // Hash the combination of child hashes
      const leftHash = await this.computeNodeHash(this.leftChild(nodeIndex));
      const rightHash = await this.computeNodeHash(this.rightChild(nodeIndex));
      return await this.hashCombination(leftHash, rightHash);
    }
  }

  /**
   * Find the next available leaf index
   * @returns {number} Next leaf index
   * @private
   */
  findNextLeafIndex() {
    // Find the leftmost blank leaf position
    for (let i = 0; i < this.nodes.length; i++) {
      if (this.isLeafPosition(i) && (!this.nodes[i] || this.nodes[i].type === 'blank')) {
        return i;
      }
    }
    
    // If no blank positions, add at the end
    return this.getNextLeafPosition();
  }

  /**
   * Check if an index is a valid leaf position
   * @param {number} index - Index to check
   * @returns {boolean} True if valid leaf position
   * @private
   */
  isLeafPosition(index) {
    // In a complete binary tree, leaf positions follow a pattern
    // This is a simplified check
    return index >= Math.floor(this.nodes.length / 2);
  }

  /**
   * Get the next leaf position for tree expansion
   * @returns {number} Next leaf position
   * @private
   */
  getNextLeafPosition() {
    // Calculate next power of 2 expansion
    const currentSize = this.nodes.length;
    const nextPowerOf2 = Math.pow(2, Math.ceil(Math.log2(currentSize + 1)));
    return nextPowerOf2 - 1;
  }

  /**
   * Ensure tree has at least the specified size
   * @param {number} minSize - Minimum tree size
   * @private
   */
  ensureTreeSize(minSize) {
    while (this.nodes.length < minSize) {
      this.nodes.push({ type: 'blank' });
    }
  }

  /**
   * Update tree depth
   * @private
   */
  updateDepth() {
    if (this.nodes.length === 0) {
      this.depth = 0;
    } else {
      this.depth = Math.floor(Math.log2(this.nodes.length)) + 1;
    }
  }

  // Cryptographic operations (placeholders for actual implementation)

  /**
   * Combine two public keys
   * @param {ArrayBuffer} key1 - First public key
   * @param {ArrayBuffer} key2 - Second public key
   * @returns {Promise<ArrayBuffer>} Combined public key
   * @private
   */
  async combinePublicKeys(key1, key2) {
    // Placeholder: actual implementation would use proper key combination
    const combined = new Uint8Array(key1.byteLength);
    const view1 = new Uint8Array(key1);
    const view2 = new Uint8Array(key2);
    
    for (let i = 0; i < combined.length; i++) {
      combined[i] = view1[i] ^ view2[i]; // Simple XOR for demo
    }
    
    return combined.buffer;
  }

  /**
   * Generate a random secret
   * @returns {ArrayBuffer} Random secret
   * @private
   */
  generateRandomSecret() {
    const secret = new Uint8Array(32);
    crypto.getRandomValues(secret);
    return secret.buffer;
  }

  /**
   * Derive path secret
   * @param {ArrayBuffer} inputSecret - Input secret
   * @param {number} nodeIndex - Node index for derivation context
   * @returns {Promise<ArrayBuffer>} Derived secret
   * @private
   */
  async derivePathSecret(inputSecret, nodeIndex) {
    // Placeholder: actual implementation would use HKDF
    const context = new Uint8Array(4);
    new DataView(context.buffer).setUint32(0, nodeIndex, false);
    
    // Simple derivation for demo
    const derived = new Uint8Array(32);
    const input = new Uint8Array(inputSecret);
    
    for (let i = 0; i < 32; i++) {
      derived[i] = input[i] ^ context[i % 4];
    }
    
    return derived.buffer;
  }

  /**
   * Hash a key package
   * @param {Object} keyPackage - Key package to hash
   * @returns {Promise<ArrayBuffer>} Hash value
   * @private
   */
  async hashKeyPackage(keyPackage) {
    // Placeholder: actual implementation would use proper serialization and hashing
    const encoder = new TextEncoder();
    const data = encoder.encode(JSON.stringify(keyPackage));
    return await crypto.subtle.digest('SHA-256', data);
  }

  /**
   * Hash combination of two hashes
   * @param {ArrayBuffer} hash1 - First hash
   * @param {ArrayBuffer} hash2 - Second hash
   * @returns {Promise<ArrayBuffer>} Combined hash
   * @private
   */
  async hashCombination(hash1, hash2) {
    const combined = new Uint8Array(hash1.byteLength + hash2.byteLength);
    combined.set(new Uint8Array(hash1), 0);
    combined.set(new Uint8Array(hash2), hash1.byteLength);
    
    return await crypto.subtle.digest('SHA-256', combined);
  }

  // Utility methods

  /**
   * Get tree statistics
   * @returns {Object} Tree statistics
   */
  getStatistics() {
    const leafCount = this.nodes.filter(node => node && node.type === 'leaf').length;
    const blankCount = this.nodes.filter(node => node && node.type === 'blank').length;
    const parentCount = this.nodes.filter(node => node && node.type === 'parent').length;
    
    return {
      totalNodes: this.nodes.length,
      leafNodes: leafCount,
      parentNodes: parentCount,
      blankNodes: blankCount,
      depth: this.depth,
      size: this.size
    };
  }

  /**
   * Validate tree structure
   * @returns {boolean} True if tree is valid
   */
  validateTree() {
    try {
      // Check that all parent nodes have valid children
      for (let i = 0; i < this.nodes.length; i++) {
        const node = this.nodes[i];
        if (node && node.type === 'parent') {
          const leftChild = this.nodes[this.leftChild(i)];
          const rightChild = this.nodes[this.rightChild(i)];
          
          if (!leftChild || !rightChild) {
            return false;
          }
        }
      }
      
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Export tree state for serialization
   * @returns {Object} Serializable tree state
   */
  exportState() {
    return {
      nodes: this.nodes.map(node => ({
        ...node,
        // Remove non-serializable crypto keys
        privateKey: null
      })),
      size: this.size,
      depth: this.depth
    };
  }

  /**
   * Import tree state from serialization
   * @param {Object} state - Tree state to import
   */
  importState(state) {
    this.nodes = state.nodes || [];
    this.size = state.size || 0;
    this.depth = state.depth || 0;
  }
}
