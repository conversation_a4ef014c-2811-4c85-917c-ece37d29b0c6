/**
 * Protocol Interface - Unified abstraction for OTR and MLS protocols
 * 
 * This module provides a common interface for both OTR and MLS protocols,
 * enabling seamless protocol switching and unified session management.
 */

import { EventEmitter } from 'events';

/**
 * Base protocol interface that all protocol implementations must extend
 */
export class ProtocolInterface extends EventEmitter {
  /**
   * Create a protocol interface
   * @param {string} type - Protocol type ('OTR' | 'MLS')
   * @param {string|number} version - Protocol version
   * @param {Object} capabilities - Protocol capabilities
   */
  constructor(type, version, capabilities = {}) {
    super();
    this.type = type;
    this.version = version;
    this.capabilities = capabilities;
    this.state = 'UNINITIALIZED';
    this.participants = [];
    this.sessionId = null;
  }

  // Abstract methods that must be implemented by concrete protocols
  
  /**
   * Initialize a new session
   * @param {Array<string>} participants - List of participant identifiers
   * @param {Object} options - Session options
   * @returns {Promise<void>}
   */
  async initializeSession(participants, options = {}) {
    throw new Error('initializeSession must be implemented by concrete protocol');
  }

  /**
   * Send a message through the protocol
   * @param {string} content - Message content
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Encrypted message object
   */
  async sendMessage(content, context = {}) {
    throw new Error('sendMessage must be implemented by concrete protocol');
  }

  /**
   * Receive and process an incoming message
   * @param {Object} message - Encrypted message object
   * @param {Object} context - Message context
   * @returns {Promise<Object>} Decrypted message result
   */
  async receiveMessage(message, context = {}) {
    throw new Error('receiveMessage must be implemented by concrete protocol');
  }

  /**
   * Add a participant to the session
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async addParticipant(participant) {
    throw new Error('addParticipant must be implemented by concrete protocol');
  }

  /**
   * Remove a participant from the session
   * @param {string} participant - Participant identifier
   * @returns {Promise<void>}
   */
  async removeParticipant(participant) {
    throw new Error('removeParticipant must be implemented by concrete protocol');
  }

  /**
   * Verify a participant's identity
   * @param {string} participant - Participant identifier
   * @param {string} method - Verification method
   * @returns {Promise<boolean>}
   */
  async verifyParticipant(participant, method = 'fingerprint') {
    throw new Error('verifyParticipant must be implemented by concrete protocol');
  }

  /**
   * End the current session
   * @returns {Promise<void>}
   */
  async endSession() {
    throw new Error('endSession must be implemented by concrete protocol');
  }

  // Common interface methods with default implementations

  /**
   * Get protocol capabilities
   * @returns {Object} Capabilities object
   */
  getCapabilities() {
    return {
      type: this.type,
      version: this.version,
      maxParticipants: this.capabilities.maxParticipants || 2,
      supportsDeniability: this.capabilities.supportsDeniability || false,
      supportsGroupMessaging: this.capabilities.supportsGroupMessaging || false,
      supportsForwardSecrecy: this.capabilities.supportsForwardSecrecy || true,
      supportsPostCompromiseSecurity: this.capabilities.supportsPostCompromiseSecurity || false,
      ...this.capabilities
    };
  }

  /**
   * Get security properties of the protocol
   * @returns {Object} Security properties
   */
  getSecurityProperties() {
    const caps = this.getCapabilities();
    return {
      confidentiality: true, // All protocols provide confidentiality
      integrity: true, // All protocols provide integrity
      authenticity: true, // All protocols provide authenticity
      deniability: caps.supportsDeniability,
      forwardSecrecy: caps.supportsForwardSecrecy,
      postCompromiseSecurity: caps.supportsPostCompromiseSecurity,
      groupAuthentication: caps.supportsGroupMessaging
    };
  }

  /**
   * Get performance characteristics
   * @returns {Object} Performance characteristics
   */
  getPerformanceCharacteristics() {
    return {
      setupLatency: 'medium', // Protocol-specific override
      messageLatency: 'low',
      scalability: this.capabilities.maxParticipants > 2 ? 'high' : 'low',
      memoryUsage: 'medium',
      cpuUsage: 'medium'
    };
  }

  /**
   * Check if protocol supports a specific feature
   * @param {string} feature - Feature name
   * @returns {boolean}
   */
  supportsFeature(feature) {
    const caps = this.getCapabilities();
    const featureMap = {
      'deniability': caps.supportsDeniability,
      'groupMessaging': caps.supportsGroupMessaging,
      'forwardSecrecy': caps.supportsForwardSecrecy,
      'postCompromiseSecurity': caps.supportsPostCompromiseSecurity,
      'participantVerification': true, // All protocols support this
      'messageOrdering': caps.supportsMessageOrdering || false,
      'offlineMessaging': caps.supportsOfflineMessaging || false
    };

    return featureMap[feature] || false;
  }

  /**
   * Get current session state
   * @returns {Object} Session state information
   */
  getSessionState() {
    return {
      type: this.type,
      version: this.version,
      state: this.state,
      sessionId: this.sessionId,
      participants: [...this.participants],
      isActive: this.state === 'ACTIVE',
      isSecure: this.state === 'ACTIVE' || this.state === 'ENCRYPTED'
    };
  }

  /**
   * Validate protocol compatibility with another protocol
   * @param {ProtocolInterface} otherProtocol - Other protocol instance
   * @returns {boolean}
   */
  isCompatibleWith(otherProtocol) {
    if (this.type === otherProtocol.type) {
      return this.version === otherProtocol.version;
    }
    return false; // Different protocols are not directly compatible
  }

  /**
   * Get migration compatibility information
   * @param {string} targetProtocol - Target protocol type
   * @returns {Object} Migration compatibility info
   */
  getMigrationCompatibility(targetProtocol) {
    const compatibility = {
      canMigrateTo: false,
      requirements: [],
      preservedProperties: [],
      lostProperties: []
    };

    if (this.type === 'OTR' && targetProtocol === 'MLS') {
      compatibility.canMigrateTo = this.participants.length >= 2;
      compatibility.requirements = ['All participants must support MLS'];
      compatibility.preservedProperties = ['confidentiality', 'integrity', 'authenticity', 'forwardSecrecy'];
      compatibility.lostProperties = ['deniability'];
    } else if (this.type === 'MLS' && targetProtocol === 'OTR') {
      compatibility.canMigrateTo = this.participants.length === 2;
      compatibility.requirements = ['Conversation must have exactly 2 participants'];
      compatibility.preservedProperties = ['confidentiality', 'integrity', 'authenticity', 'forwardSecrecy'];
      compatibility.lostProperties = ['groupMessaging', 'postCompromiseSecurity'];
    }

    return compatibility;
  }

  // Event handling helpers

  /**
   * Emit a protocol event
   * @param {string} event - Event name
   * @param {Object} data - Event data
   */
  emitProtocolEvent(event, data = {}) {
    this.emit(event, {
      protocol: this.type,
      version: this.version,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      ...data
    });
  }

  /**
   * Set session state and emit state change event
   * @param {string} newState - New state
   * @param {Object} metadata - Additional metadata
   */
  setState(newState, metadata = {}) {
    const oldState = this.state;
    this.state = newState;
    
    this.emitProtocolEvent('stateChanged', {
      oldState,
      newState,
      ...metadata
    });
  }

  // Utility methods

  /**
   * Generate a unique session ID
   * @returns {string} Session ID
   */
  generateSessionId() {
    return `${this.type.toLowerCase()}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate participant list
   * @param {Array<string>} participants - Participant list
   * @throws {Error} If participant list is invalid
   */
  validateParticipants(participants) {
    if (!Array.isArray(participants)) {
      throw new Error('Participants must be an array');
    }

    if (participants.length < 2) {
      throw new Error('At least 2 participants required');
    }

    const caps = this.getCapabilities();
    if (participants.length > caps.maxParticipants) {
      throw new Error(`Protocol ${this.type} supports maximum ${caps.maxParticipants} participants`);
    }

    // Check for duplicates
    const uniqueParticipants = new Set(participants);
    if (uniqueParticipants.size !== participants.length) {
      throw new Error('Duplicate participants not allowed');
    }
  }

  /**
   * Clean up protocol resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    this.setState('CLEANUP');
    this.removeAllListeners();
    this.participants = [];
    this.sessionId = null;
    this.setState('TERMINATED');
  }
}

/**
 * Protocol capability constants
 */
export const PROTOCOL_CAPABILITIES = {
  OTR: {
    maxParticipants: 2,
    supportsDeniability: true,
    supportsGroupMessaging: false,
    supportsForwardSecrecy: true,
    supportsPostCompromiseSecurity: false,
    supportsMessageOrdering: false,
    supportsOfflineMessaging: false
  },
  MLS: {
    maxParticipants: 10000,
    supportsDeniability: false,
    supportsGroupMessaging: true,
    supportsForwardSecrecy: true,
    supportsPostCompromiseSecurity: true,
    supportsMessageOrdering: true,
    supportsOfflineMessaging: true
  }
};

/**
 * Protocol state constants
 */
export const PROTOCOL_STATES = {
  UNINITIALIZED: 'UNINITIALIZED',
  INITIALIZING: 'INITIALIZING',
  AWAITING_RESPONSE: 'AWAITING_RESPONSE',
  ACTIVE: 'ACTIVE',
  ENCRYPTED: 'ENCRYPTED',
  ERROR: 'ERROR',
  CLEANUP: 'CLEANUP',
  TERMINATED: 'TERMINATED'
};

/**
 * Protocol error classes
 */
export class ProtocolError extends Error {
  constructor(message, code, protocol) {
    super(message);
    this.name = 'ProtocolError';
    this.code = code;
    this.protocol = protocol;
  }
}

export class ProtocolSelectionError extends ProtocolError {
  constructor(message, protocol) {
    super(message, 'PROTOCOL_SELECTION_ERROR', protocol);
    this.name = 'ProtocolSelectionError';
  }
}

export class MigrationError extends ProtocolError {
  constructor(message, fromProtocol, toProtocol) {
    super(message, 'MIGRATION_ERROR', fromProtocol);
    this.name = 'MigrationError';
    this.fromProtocol = fromProtocol;
    this.toProtocol = toProtocol;
  }
}
