# MLS-OTR Integration Implementation Plan
## Actionable Steps for Dual Protocol System

**Version:** 1.0  
**Date:** June 2025  
**Status:** Implementation Ready  
**Project:** WebOTR MLS Integration

---

## Implementation Overview

This document provides actionable implementation steps for integrating MLS alongside OTR in the WebOTR project, enabling dynamic protocol switching based on conversation context.

### Project Impact Analysis

**Current WebOTR Architecture Strengths:**
- Mature OTRv3 implementation with comprehensive testing
- Existing protocol abstraction via `VersionNegotiation` and `ProtocolVersionFactory`
- Platform adapter architecture ready for extension
- Comprehensive test framework with <PERSON><PERSON>, <PERSON>wright, and reference testing

**Integration Points:**
- Extend existing `src/core/protocol/` structure
- Leverage current `src/core/session/` management
- Enhance platform adapters in `src/platforms/`
- Expand testing framework for dual protocol scenarios

---

## Phase 1: Foundation & Protocol Abstraction (Weeks 1-3)

### Task 1.1: Protocol Interface Design
**Duration:** 3 days  
**Priority:** Critical  

**Deliverables:**
```javascript
// src/core/protocol/ProtocolInterface.js
export class ProtocolInterface {
  constructor(type, version, capabilities) {
    this.type = type; // 'OTR' | 'MLS'
    this.version = version;
    this.capabilities = capabilities;
  }
  
  // Unified protocol operations
  async initializeSession(participants, options) {}
  async sendMessage(content, context) {}
  async receiveMessage(message, context) {}
  async addParticipant(participant) {}
  async removeParticipant(participant) {}
  async endSession() {}
  
  // Protocol metadata
  getSecurityProperties() {}
  getPerformanceCharacteristics() {}
  supportsFeature(feature) {}
}
```

**TDD Implementation:**
```javascript
// tests/protocol/protocol-interface.test.js
describe('ProtocolInterface', () => {
  test('should provide unified interface for both OTR and MLS', () => {
    const otrProtocol = new OTRProtocol(3);
    const mlsProtocol = new MLSProtocol('MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519');
    
    expect(otrProtocol).toBeInstanceOf(ProtocolInterface);
    expect(mlsProtocol).toBeInstanceOf(ProtocolInterface);
  });
  
  test('should expose protocol-specific capabilities', () => {
    const otr = new OTRProtocol(3);
    expect(otr.supportsFeature('deniability')).toBe(true);
    expect(otr.supportsFeature('groupMessaging')).toBe(false);
    
    const mls = new MLSProtocol();
    expect(mls.supportsFeature('groupMessaging')).toBe(true);
    expect(mls.supportsFeature('deniability')).toBe(false);
  });
});
```

### Task 1.2: Protocol Selection Engine
**Duration:** 4 days  
**Priority:** Critical  

**Deliverables:**
```javascript
// src/core/protocol/ProtocolSelector.js
export class ProtocolSelector {
  constructor(policy = {}) {
    this.policy = {
      preferOTRFor2Party: true,
      requireMLSForGroups: true,
      allowProtocolMigration: true,
      ...policy
    };
  }
  
  selectProtocol(conversationContext) {
    const { participantCount, existingProtocol, userPreference, capabilities } = conversationContext;
    
    // Group conversations require MLS
    if (participantCount >= 3) {
      if (!capabilities.supportsMLs) {
        throw new ProtocolSelectionError('MLS required for groups but not supported');
      }
      return { protocol: 'MLS', reason: 'GROUP_CONVERSATION' };
    }
    
    // Two-party conversation logic
    if (existingProtocol === 'OTR') {
      return { protocol: 'OTR', reason: 'EXISTING_SESSION' };
    }
    
    if (userPreference === 'MLS' && capabilities.supportsMLs) {
      return { protocol: 'MLS', reason: 'USER_PREFERENCE' };
    }
    
    return { protocol: 'OTR', reason: 'DEFAULT' };
  }
  
  canMigrate(fromProtocol, toProtocol, context) {
    // Migration rules
    if (fromProtocol === 'OTR' && toProtocol === 'MLS') {
      return context.participantCount >= 3;
    }
    return false;
  }
}
```

**BDD Scenarios:**
```gherkin
# features/protocol-selection.feature
Feature: Intelligent Protocol Selection
  As a user
  I want the system to automatically select the appropriate protocol
  So that I get optimal security and functionality for each conversation

  Scenario: Two-party conversation defaults to OTR
    Given I start a conversation with one other person
    And both parties support OTR and MLS
    When the protocol is selected
    Then OTR should be chosen
    And the reason should be "DEFAULT"

  Scenario: Group conversation requires MLS
    Given I start a conversation with 2 other people
    And all parties support MLS
    When the protocol is selected
    Then MLS should be chosen
    And the reason should be "GROUP_CONVERSATION"

  Scenario: Protocol migration when adding participants
    Given I have an OTR conversation with one person
    When a third person is invited
    Then the system should suggest migrating to MLS
    And preserve existing message history
```

### Task 1.3: Enhanced Session Management
**Duration:** 5 days  
**Priority:** High  

**Objective:** Extend existing `UXOtrSession` to support dual protocols

**Deliverables:**
```javascript
// src/core/session/UnifiedSession.js
export class UnifiedSession extends EventEmitter {
  constructor(options = {}) {
    super();
    this.protocolSelector = new ProtocolSelector(options.policy);
    this.currentProtocol = null;
    this.protocolHandlers = new Map();
    this.migrationState = null;
  }
  
  async initializeSession(participants, options = {}) {
    const context = this.buildConversationContext(participants, options);
    const selection = this.protocolSelector.selectProtocol(context);
    
    this.currentProtocol = await this.createProtocolHandler(selection.protocol);
    await this.currentProtocol.initializeSession(participants, options);
    
    this.emit('sessionInitialized', { protocol: selection.protocol, reason: selection.reason });
  }
  
  async migrateProtocol(toProtocol, reason) {
    if (!this.protocolSelector.canMigrate(this.currentProtocol.type, toProtocol, this.getContext())) {
      throw new MigrationError(`Cannot migrate from ${this.currentProtocol.type} to ${toProtocol}`);
    }
    
    this.migrationState = { from: this.currentProtocol.type, to: toProtocol, reason };
    this.emit('migrationStarted', this.migrationState);
    
    // Perform migration
    const newProtocol = await this.createProtocolHandler(toProtocol);
    await this.performMigration(this.currentProtocol, newProtocol);
    
    this.currentProtocol = newProtocol;
    this.migrationState = null;
    this.emit('migrationCompleted', { protocol: toProtocol });
  }
}
```

---

## Phase 2: MLS Core Implementation (Weeks 4-8)

### Task 2.1: MLS Cryptographic Primitives
**Duration:** 6 days  
**Priority:** Critical  

**Objective:** Implement MLS-specific crypto operations

**Deliverables:**
```javascript
// src/core/crypto/mls/MLSCrypto.js
export class MLSCrypto {
  constructor(cipherSuite) {
    this.cipherSuite = cipherSuite;
    this.hpke = new HPKEContext(cipherSuite);
  }
  
  async generateKeyPackage(identity) {
    const keyPair = await this.generateKeyPair();
    const credential = await this.createCredential(identity);
    
    return {
      version: 1,
      cipherSuite: this.cipherSuite,
      initKey: keyPair.publicKey,
      credential,
      extensions: [],
      signature: await this.signKeyPackage(keyPair.privateKey, credential)
    };
  }
  
  async deriveSecret(inputKeyMaterial, label, context, length) {
    return await this.hkdfExpand(inputKeyMaterial, label, context, length);
  }
}
```

**TDD Test Suite:**
```javascript
// tests/crypto/mls-crypto.test.js
describe('MLSCrypto', () => {
  let crypto;
  
  beforeEach(() => {
    crypto = new MLSCrypto('MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519');
  });
  
  test('should generate valid KeyPackage', async () => {
    const keyPackage = await crypto.generateKeyPackage('<EMAIL>');
    
    expect(keyPackage.version).toBe(1);
    expect(keyPackage.cipherSuite).toBe('MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519');
    expect(keyPackage.initKey).toHaveLength(32); // X25519 public key
    expect(keyPackage.signature).toBeDefined();
  });
  
  test('should derive secrets using HKDF', async () => {
    const ikm = new Uint8Array(32).fill(1);
    const secret = await crypto.deriveSecret(ikm, 'test', new Uint8Array(0), 32);
    
    expect(secret).toHaveLength(32);
    expect(secret).not.toEqual(ikm);
  });
});
```

### Task 2.2: TreeKEM Implementation
**Duration:** 8 days  
**Priority:** Critical  

**Objective:** Implement binary tree key management for MLS

**Deliverables:**
```javascript
// src/core/protocol/mls/TreeKEM.js
export class RatchetTree {
  constructor() {
    this.nodes = [];
    this.size = 0;
  }
  
  // Tree math operations
  parent(index) {
    return Math.floor((index - 1) / 2);
  }
  
  leftChild(index) {
    return 2 * index + 1;
  }
  
  rightChild(index) {
    return 2 * index + 2;
  }
  
  sibling(index) {
    return index % 2 === 0 ? index - 1 : index + 1;
  }
  
  // Tree operations
  async addLeaf(keyPackage) {
    const leafIndex = this.size;
    this.nodes[leafIndex] = {
      type: 'leaf',
      keyPackage,
      privateKey: null
    };
    
    await this.updatePathToRoot(leafIndex);
    this.size++;
    
    return leafIndex;
  }
  
  async removeLeaf(leafIndex) {
    this.nodes[leafIndex] = { type: 'blank' };
    await this.updatePathToRoot(leafIndex);
  }
  
  async updatePathToRoot(leafIndex) {
    let currentIndex = leafIndex;
    
    while (currentIndex > 0) {
      const parentIndex = this.parent(currentIndex);
      await this.updateParentNode(parentIndex);
      currentIndex = parentIndex;
    }
  }
}
```

---

## Phase 3: Testing Strategy Implementation (Weeks 9-11)

### Task 3.1: TDD Test Suite Development
**Duration:** 5 days  
**Priority:** High  

**Objective:** Comprehensive unit testing for all components

**Test Structure:**
```
tests/
├── protocol/
│   ├── protocol-interface.test.js
│   ├── protocol-selector.test.js
│   └── unified-session.test.js
├── mls/
│   ├── mls-crypto.test.js
│   ├── treekem.test.js
│   ├── state-machine.test.js
│   └── message-processing.test.js
├── integration/
│   ├── otr-mls-migration.test.js
│   ├── protocol-switching.test.js
│   └── platform-compatibility.test.js
└── e2e/
    ├── dual-protocol-conversations.test.js
    └── interoperability.test.js
```

### Task 3.2: BDD Scenario Implementation
**Duration:** 4 days  
**Priority:** High  

**Objective:** Behavior-driven testing for user scenarios

**Feature Files:**
```gherkin
# features/protocol-migration.feature
Feature: Seamless Protocol Migration
  As a user
  I want to seamlessly switch between OTR and MLS
  So that I can maintain secure conversations regardless of participant count

  Scenario: Adding participant triggers MLS migration
    Given I have an active OTR conversation with Bob
    And the conversation has exchanged 5 messages
    When I invite Charlie to join the conversation
    Then the system should prompt for MLS migration
    And existing message history should be preserved
    And all three participants should establish MLS group keys
    And future messages should use MLS encryption

  Scenario: Graceful fallback when MLS unavailable
    Given I try to start a group conversation
    And one participant doesn't support MLS
    Then the system should notify about limited functionality
    And offer to continue with OTR for supported pairs
    Or suggest alternative communication methods
```

### Task 3.3: Integration Testing Framework
**Duration:** 6 days  
**Priority:** High  

**Objective:** End-to-end testing of dual protocol system

**Test Scenarios:**
- Protocol selection accuracy
- Migration success rates
- Performance benchmarks
- Security property validation
- Platform compatibility
- Error handling and recovery

---

## Implementation Timeline Summary

**Week 1-3: Foundation**
- Protocol abstraction layer
- Selection engine
- Enhanced session management

**Week 4-8: MLS Core**
- Cryptographic primitives
- TreeKEM implementation
- State machine and messaging

**Week 9-11: Testing**
- Comprehensive test suite
- BDD scenarios
- Integration testing

**Week 12-14: Integration**
- Platform adapter updates
- UI/UX enhancements
- Performance optimization

**Week 15-16: Validation**
- Security audit
- Interoperability testing
- Production readiness

---

## Next Steps

1. **Immediate Actions:**
   - Set up development branch for MLS integration
   - Install required dependencies (HPKE, CBOR libraries)
   - Create initial project structure

2. **Development Setup:**
   - Configure Jest for dual protocol testing
   - Set up Cucumber.js for BDD scenarios
   - Establish CI/CD pipeline updates

3. **Team Coordination:**
   - Assign tasks based on expertise
   - Set up regular integration checkpoints
   - Plan security review milestones
