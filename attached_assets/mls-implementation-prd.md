# Product Requirements Document: Dual Protocol Integration
## MLS-OTR Hybrid Messaging System with Dynamic Protocol Switching

**Version:** 2.0
**Date:** June 2025
**Status:** Implementation Ready
**Project:** WebOTR MLS Integration

---

## Executive Summary

This PRD outlines the integration of Messaging Layer Security (MLS) protocol alongside our existing OTR implementation, creating a hybrid messaging system capable of dynamic protocol switching. The system will intelligently select between OTR and MLS based on conversation context, participant count, and security requirements while maintaining seamless user experience.

### Key Objectives
- **Dual Protocol Support**: Implement RFC 9420 compliant MLS alongside existing OTRv3 functionality
- **Intelligent Protocol Selection**: Automatic protocol selection based on conversation type and participant count
- **Seamless Protocol Migration**: Runtime switching between OTR and MLS during conversation lifecycle
- **Unified User Experience**: Single interface for both protocols with transparent operation
- **Backward Compatibility**: Full compatibility with existing OTR-only clients
- **Interoperability**: Support for MLS federation with other implementations

---

## 1. Product Vision & Strategic Alignment

### 1.1 Problem Statement & Current State Analysis

**Current WebOTR Capabilities:**
- Mature OTRv3 implementation with version negotiation
- Platform adapters for Discord, Teams, Slack, and generic platforms
- Comprehensive testing framework with libOTR compatibility
- Browser extension architecture with UI integration

**Identified Limitations:**
- **Two-party restriction**: OTR limited to 1:1 conversations
- **Group messaging gap**: No secure group communication capability
- **Protocol rigidity**: Single protocol approach limits use cases
- **Scalability constraints**: Cannot handle modern group communication needs
- **Interoperability barriers**: Limited to OTR-compatible clients only

**User Requirements Evolution:**
- Secure group messaging for 3-10,000+ participants
- Protocol flexibility based on conversation context
- Seamless migration between conversation types
- Interoperability with MLS-adopting platforms (Matrix, Wire, RCS)
- Maintained deniability for sensitive 1:1 conversations

### 1.2 Solution Architecture Overview

**Hybrid Protocol System:**
```
┌─────────────────────────────────────────────────────────────┐
│                    WebOTR Unified Interface                 │
├─────────────────────────────────────────────────────────────┤
│              Protocol Selection Engine                      │
│  ┌─────────────────┐           ┌─────────────────────────┐  │
│  │   OTR Handler   │           │     MLS Handler         │  │
│  │   - 1:1 chats   │           │   - Group messaging     │  │
│  │   - Deniability │           │   - Scalable groups     │  │
│  │   - V2/V3 compat│           │   - RFC 9420 compliant │  │
│  └─────────────────┘           └─────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                 Shared Infrastructure                       │
│  • Crypto Engine  • Storage  • Transport  • UI/UX         │
└─────────────────────────────────────────────────────────────┘
```

**Protocol Selection Logic:**
- **2-party conversations**: OTR by default, MLS optional
- **3+ party conversations**: MLS mandatory
- **Migration scenarios**: Seamless protocol switching
- **Legacy support**: Graceful fallback to OTR-only mode

### 1.3 Success Metrics & KPIs

**Technical Performance:**
- Support groups up to 10,000 members with <100ms key update latency
- Achieve 100% RFC 9420 compliance verified by official test vectors
- Pass interoperability testing with 3+ reference MLS implementations
- Maintain <5% performance overhead for existing OTR conversations
- Zero regression in existing OTR functionality

**User Experience:**
- <2 second protocol selection and initialization
- Transparent protocol switching with <1% user-visible errors
- 100% backward compatibility with existing OTR contacts
- Support for all current platform integrations (Discord, Teams, Slack)

**Security & Compliance:**
- Pass security audit for both protocols
- Maintain forward secrecy and post-compromise security
- Support for enterprise compliance requirements
- Comprehensive test coverage >95% for both protocols

---

## 2. Technical Architecture & System Design

### 2.1 Protocol Abstraction Layer

**Unified Protocol Interface:**
```javascript
// src/core/protocol/ProtocolInterface.js
class ProtocolInterface {
  // Common protocol operations
  async initializeSession(participants, options) {}
  async sendMessage(content, context) {}
  async receiveMessage(message, context) {}
  async addParticipant(participant) {}
  async removeParticipant(participant) {}
  async verifyParticipant(participant, method) {}
  async endSession() {}

  // Protocol-specific capabilities
  getCapabilities() {}
  getSecurityProperties() {}
  getPerformanceCharacteristics() {}
}
```

**Protocol Selection Engine:**
```javascript
// src/core/protocol/ProtocolSelector.js
class ProtocolSelector {
  selectProtocol(conversationContext) {
    const { participantCount, existingProtocol, userPreference, capabilities } = conversationContext;

    // Selection logic
    if (participantCount >= 3) return 'MLS';
    if (existingProtocol === 'OTR' && participantCount === 2) return 'OTR';
    if (userPreference === 'MLS' && capabilities.supportsMLs) return 'MLS';
    return 'OTR'; // Default fallback
  }

  canMigrate(fromProtocol, toProtocol, context) {}
  planMigration(fromProtocol, toProtocol, context) {}
}
```

### 2.2 MLS Core Implementation

**TreeKEM Engine Integration:**
```javascript
// src/core/protocol/mls/TreeKEM.js
class TreeKEMEngine {
  constructor(cipherSuite) {
    this.cipherSuite = cipherSuite;
    this.tree = new RatchetTree();
  }

  // Core TreeKEM operations
  async addLeaf(keyPackage) {}
  async removeLeaf(leafIndex) {}
  async updatePath(leafIndex, pathSecrets) {}
  async computeTreeHash() {}
}
```

**MLS State Machine:**
```javascript
// src/core/protocol/mls/MLSStateMachine.js
class MLSStateMachine {
  constructor(groupId, epoch = 0) {
    this.groupId = groupId;
    this.epoch = epoch;
    this.pendingProposals = new Map();
    this.ratchetTree = new RatchetTree();
  }

  async processProposal(proposal) {}
  async createCommit(proposalIds) {}
  async processCommit(commit) {}
  async advanceEpoch() {}
}
```

### 2.3 Integration with Existing WebOTR Architecture

**Leveraging Current Infrastructure:**
- **Platform Adapters**: Extend existing Discord/Teams/Slack adapters for MLS
- **Crypto Engine**: Enhance with MLS-specific primitives (HPKE, TreeKEM)
- **Session Management**: Unified session handling for both protocols
- **UI Components**: Protocol-aware status indicators and controls

**Storage Layer Enhancement:**
```javascript
// src/core/storage/ProtocolStorage.js
class ProtocolStorage {
  // Unified storage for both OTR and MLS
  async storeSession(sessionId, protocol, state) {}
  async loadSession(sessionId) {}
  async storeGroupState(groupId, epoch, state) {}
  async loadGroupState(groupId, epoch) {}
}
```

### 2.4 Dependencies & External Libraries

**Core Cryptographic Dependencies:**
- **WebCrypto API**: Primary crypto provider for browser environments
- **Noble Crypto**: Fallback for Node.js and advanced crypto operations
- **HPKE Implementation**: For MLS hybrid public key encryption
- **CBOR Library**: Message encoding/decoding for MLS
- **TreeKEM Library**: Binary tree key management

**Testing & Development Dependencies:**
- **MLS Test Vectors**: RFC 9420 official test vectors
- **Cucumber.js**: BDD testing framework
- **Jest**: Enhanced for dual protocol testing
- **Playwright**: E2E testing for protocol switching scenarios

---

## 3. Development Phases

### Phase 1: Foundation & Infrastructure (Weeks 1-4)

#### 3.1.1 Development Setup
**TDD Stage:**
```javascript
// test/setup/mls-test-environment.spec.js
describe('MLS Test Environment', () => {
  it('should load MLS test vectors from RFC 9420', async () => {
    const vectors = await loadMLSTestVectors();
    expect(vectors).toHaveProperty('keyPackages');
    expect(vectors).toHaveProperty('welcomeMessages');
  });
});
```

**BDD Stage:**
```gherkin
# features/mls-setup.feature
Feature: MLS Development Environment
  As a developer
  I want a properly configured MLS development environment
  So that I can implement RFC 9420 compliant functionality

  Scenario: Loading official test vectors
    Given the RFC 9420 test vectors are available
    When I initialize the test environment
    Then all test vectors should be loaded and validated
    And crypto primitives should be available
```

**Implementation Tasks:**
- Set up TypeScript configuration with strict typing
- Configure Jest for TDD with crypto mocking support
- Implement Cucumber.js for BDD scenarios
- Create test vector loader and validator
- Establish CI/CD pipeline with test automation

#### 3.1.2 Crypto Primitive Layer
**TDD Stage:**
```javascript
// test/crypto/mls-crypto-primitives.spec.js
describe('MLS Crypto Primitives', () => {
  describe('HPKE Operations', () => {
    it('should perform KEM encapsulation/decapsulation', async () => {
      const suite = new CipherSuite(MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519);
      const keyPair = await suite.generateKeyPair();
      const { enc, sharedSecret } = await suite.kem.encapsulate(keyPair.public);
      const decapsulated = await suite.kem.decapsulate(enc, keyPair.private);
      expect(decapsulated).toEqual(sharedSecret);
    });
  });
});
```

**BDD Stage:**
```gherkin
Feature: Cryptographic Operations
  Scenario: Generating ephemeral key pairs
    Given I have selected the MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519 ciphersuite
    When I generate a new key pair
    Then the public key should be 32 bytes
    And the private key should be 32 bytes
    And the keys should be valid for X25519 operations
```

### Phase 2: Core MLS Protocol (Weeks 5-10)

#### 3.2.1 TreeKEM Implementation
**TDD Stage:**
```javascript
// test/treekem/ratchet-tree.spec.js
describe('Ratchet Tree Operations', () => {
  let tree;
  
  beforeEach(() => {
    tree = new RatchetTree();
  });

  it('should correctly compute tree math for parent nodes', () => {
    expect(tree.parent(4)).toBe(5);
    expect(tree.parent(5)).toBe(6);
    expect(tree.sibling(4)).toBe(5);
  });

  it('should update path secrets on member addition', async () => {
    const leafNode = await tree.addLeaf(keyPackage);
    const pathSecrets = await tree.computePathSecrets(leafNode.index);
    expect(pathSecrets).toHaveLength(tree.depth);
  });
});
```

**BDD Stage:**
```gherkin
Feature: Group Key Management
  Scenario: Adding a new member to the group
    Given a group with 3 existing members
    And a new member with a valid KeyPackage
    When the new member is added via a Commit message
    Then the ratchet tree should have 4 leaves
    And all members should derive the same group secret
    And the tree should maintain its binary structure

  Scenario: Removing a member from the group
    Given a group with 5 members
    When member at index 2 is removed
    Then the leaf should be marked as blank
    And path secrets should be updated
    And remaining members should converge on new epoch
```

#### 3.2.2 State Machine & Message Processing
**TDD Stage:**
```javascript
// test/state/mls-state-machine.spec.js
describe('MLS State Machine', () => {
  it('should enforce proposal before commit semantics', async () => {
    const group = new MLSGroup();
    const proposal = createAddProposal(newMember);
    
    await group.processProposal(proposal);
    expect(group.pendingProposals).toHaveLength(1);
    
    const commit = await group.createCommit([proposal.id]);
    expect(commit.proposals).toContain(proposal.id);
  });

  it('should reject commits referencing unknown proposals', async () => {
    const group = new MLSGroup();
    const fakeProposalId = crypto.randomBytes(32);
    
    await expect(group.createCommit([fakeProposalId]))
      .rejects.toThrow('Unknown proposal');
  });
});
```

### Phase 3: Message Layer Security (Weeks 11-14)

#### 3.3.1 Application Message Encryption
**TDD Stage:**
```javascript
// test/messages/application-messages.spec.js
describe('Application Message Encryption', () => {
  it('should encrypt messages with forward secrecy', async () => {
    const group = await createTestGroup(3);
    const message = 'Test message';
    
    const ciphertext = await group.encrypt(message);
    const secretBefore = group.currentEpochSecret;
    
    // Advance the key schedule
    await group.ratchetForward();
    const secretAfter = group.currentEpochSecret;
    
    expect(secretBefore).not.toEqual(secretAfter);
    expect(() => group.decrypt(ciphertext)).toThrow('Old epoch');
  });
});
```

**BDD Stage:**
```gherkin
Feature: Secure Message Exchange
  Scenario: Sending encrypted messages in a group
    Given an established MLS group with 4 members
    When Alice sends "Hello, secure world!" to the group
    Then all members should decrypt the same plaintext
    And the message should include authenticated sender data
    And non-members should not be able to decrypt

  Scenario: Message ordering and replay protection
    Given a group in epoch 5
    When Bob sends 3 messages in sequence
    Then messages should have increasing generation numbers
    And replayed messages should be rejected
    And out-of-order delivery should be handled correctly
```

### Phase 4: Protocol Integration (Weeks 15-18)

#### 3.4.1 Protocol Selection & Handoff
**TDD Stage:**
```javascript
// test/integration/protocol-selection.spec.js
describe('Protocol Selection Logic', () => {
  it('should use OTR for two-party when preferred', async () => {
    const config = { preferOTR: true };
    const conversation = await createConversation(['alice', 'bob'], config);
    
    expect(conversation.protocol).toBe('OTRv3');
    expect(conversation.capabilities).toContain('deniability');
  });

  it('should force MLS for group conversations', async () => {
    const conversation = await createConversation(['alice', 'bob', 'charlie']);
    
    expect(conversation.protocol).toBe('MLS');
    expect(conversation.capabilities).toContain('group-scalability');
  });
});
```

**BDD Stage:**
```gherkin
Feature: Seamless Protocol Migration
  Scenario: Upgrading from OTR to MLS for group formation
    Given Alice and Bob are in an OTR conversation
    When Charlie is invited to join
    Then the system should prompt for protocol upgrade
    And existing message history should be preserved
    And new conversation should use MLS
    And all three members should share group keys

  Scenario: Maintaining parallel conversations
    Given Alice has an OTR conversation with Bob
    And Alice has an MLS group with Bob and Charlie
    When Alice sends a direct message to Bob
    Then it should use the existing OTR session
    And group messages should use MLS
```

### Phase 5: Interoperability Testing (Weeks 19-22)

#### 3.5.1 Reference Implementation Testing
**TDD Stage:**
```javascript
// test/interop/reference-implementations.spec.js
describe('MLS Interoperability', () => {
  const implementations = ['openmls', 'mlspp', 'go-mls'];
  
  implementations.forEach(impl => {
    it(`should exchange KeyPackages with ${impl}`, async () => {
      const ourKP = await generateKeyPackage();
      const theirKP = await loadExternalKeyPackage(impl, 'keypackage.bin');
      
      expect(await validateKeyPackage(theirKP)).toBe(true);
      expect(await sendToImplementation(impl, ourKP)).toBe(true);
    });
  });
});
```

**BDD Stage:**
```gherkin
Feature: Cross-Implementation Compatibility
  Scenario Outline: Establishing groups across implementations
    Given our implementation as the group creator
    And <implementation> as a joining member
    When we create a group and send a Welcome
    Then <implementation> should successfully join
    And both should derive identical epoch secrets
    And messages should be exchanged successfully

    Examples:
      | implementation |
      | OpenMLS        |
      | mlspp          |
      | go-mls         |
```

### Phase 6: Production Hardening (Weeks 23-26)

#### 3.6.1 Performance Optimization
**TDD Stage:**
```javascript
// test/performance/scalability.spec.js
describe('MLS Performance Benchmarks', () => {
  it('should handle 10k member groups efficiently', async () => {
    const startTime = Date.now();
    const group = await createLargeGroup(10000);
    
    const addMemberTime = Date.now();
    await group.addMember(newMemberKeyPackage);
    const addDuration = Date.now() - addMemberTime;
    
    expect(addDuration).toBeLessThan(100); // <100ms requirement
    expect(group.treeOperations).toBeLessThan(Math.log2(10000) * 2);
  });
});
```

#### 3.6.2 Security Hardening
**BDD Stage:**
```gherkin
Feature: Security Resilience
  Scenario: Handling malformed messages
    Given a properly initialized MLS group
    When receiving a malformed Commit message
    Then the message should be rejected
    And the group state should remain unchanged
    And an security event should be logged

  Scenario: Post-compromise security
    Given a group where Bob's device was compromised
    When Bob performs a key update
    And the group performs 2 epochs of updates
    Then messages encrypted with old keys should fail
    And the compromise window should be limited
```

---

## 4. Testing Strategy

### 4.1 Test Coverage Requirements
- Unit tests: >95% code coverage
- Integration tests: All protocol state transitions
- Interoperability tests: 3+ reference implementations
- Performance tests: Groups up to 10k members
- Security tests: Fuzzing, formal verification where applicable

### 4.2 Continuous Testing Pipeline
```yaml
# .github/workflows/mls-testing.yml
name: MLS Test Suite
on: [push, pull_request]
jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - run: npm test -- --coverage
      
  bdd-tests:
    runs-on: ubuntu-latest
    steps:
      - run: npm run test:cucumber
      
  interop-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        implementation: [openmls, mlspp, go-mls]
    steps:
      - run: npm run test:interop -- --impl ${{ matrix.implementation }}
```

---

## 5. Release Criteria

### 5.1 Alpha Release (Week 20)
- Core MLS functionality implemented
- Local testing with 2-100 member groups
- 80% RFC compliance
- Basic UI for protocol selection

### 5.2 Beta Release (Week 24)
- Full RFC 9420 compliance
- Interoperability verified with 2+ implementations
- Performance benchmarks met
- Security audit initiated

### 5.3 Production Release (Week 28)
- Security audit completed and issues resolved
- 10k member group support verified
- Backward compatibility maintained
- Documentation and migration guides complete

---

## 6. Risk Management

### 6.1 Technical Risks
| Risk | Impact | Mitigation |
|------|---------|------------|
| WebCrypto API limitations | High | Implement fallback to pure JS libraries |
| Tree rebalancing complexity | Medium | Implement incremental rebalancing |
| State synchronization issues | High | Implement vector clocks and conflict resolution |
| Memory usage in large groups | Medium | Implement tree pruning and lazy loading |

### 6.2 Integration Risks
| Risk | Impact | Mitigation |
|------|---------|------------|
| OTR/MLS state conflicts | High | Separate state stores with clear boundaries |
| Protocol downgrade attacks | High | Implement strict protocol negotiation |
| Message format incompatibility | Medium | Implement message version detection |

---

## 7. Future Enhancements

### 7.1 Post-Quantum Cryptography
- Implement MLS PQC extensions (IETF draft)
- Support hybrid classical/PQ ciphersuites
- Timeline: Q2 2026

### 7.2 Advanced Features
- Subgroup/channel support
- External join links
- Federated group management
- Message reactions and receipts

---

## 8. Appendices

### A. RFC 9420 Compliance Checklist
- [ ] KeyPackage generation and validation
- [ ] Welcome message processing
- [ ] Proposal/Commit state machine
- [ ] TreeKEM operations
- [ ] Message encryption/decryption
- [ ] Epoch and generation management
- [ ] PSK support
- [ ] Extension framework

### B. Ciphersuite Support Matrix
| Ciphersuite | Priority | Status |
|-------------|----------|---------|
| MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519 | Required | Phase 2 |
| MLS_128_DHKEMP256_AES128GCM_SHA256_P256 | Required | Phase 2 |
| MLS_256_DHKEMX448_AES256GCM_SHA512_Ed448 | Optional | Phase 5 |
| MLS_256_DHKEMP521_AES256GCM_SHA512_P521 | Optional | Phase 5 |

### C. Development Resources
- RFC 9420: https://www.rfc-editor.org/rfc/rfc9420.html
- MLS Test Vectors: https://github.com/mlswg/mls-implementations
- Reference Implementations: OpenMLS, mlspp, go-mls
- IETF MLS Working Group: https://datatracker.ietf.org/wg/mls/