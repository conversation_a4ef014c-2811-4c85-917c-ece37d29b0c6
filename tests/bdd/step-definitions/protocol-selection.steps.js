/**
 * Step definitions for Protocol Selection BDD scenarios
 * 
 * Implements the step definitions for the protocol selection feature file,
 * providing the glue between Gherkin scenarios and test implementation.
 */

import { Given, When, Then, Before, After } from '@cucumber/cucumber';
import { expect } from 'chai';
import { ProtocolSelector } from '../../../src/core/protocol/ProtocolSelector.js';
import { UnifiedSession } from '../../../src/core/session/UnifiedSession.js';

// Test context to share state between steps
class TestContext {
  constructor() {
    this.reset();
  }

  reset() {
    this.contacts = new Map();
    this.currentUser = null;
    this.conversationContext = null;
    this.protocolSelection = null;
    this.session = null;
    this.error = null;
    this.warnings = [];
    this.alternatives = [];
    this.policy = {
      preferOTRFor2Party: true,
      requireMLSForGroups: true,
      allowProtocolMigration: true
    };
    this.userPreference = null;
    this.securityRequirements = {};
    this.securityPriority = 'balanced';
  }
}

const testContext = new TestContext();

// Hooks
Before(function() {
  testContext.reset();
});

After(function() {
  if (testContext.session) {
    testContext.session.endSession().catch(console.error);
  }
});

// Background steps
Given('I have WebOTR installed and configured', function() {
  // Simulate WebOTR installation and configuration
  testContext.currentUser = '<EMAIL>';
  expect(testContext.currentUser).to.exist;
});

Given('I have the following contacts:', function(dataTable) {
  const contacts = dataTable.hashes();
  
  contacts.forEach(contact => {
    testContext.contacts.set(contact.name, {
      name: contact.name,
      supportsOTR: contact.supports_otr === 'true',
      supportsMLs: contact.supports_mls === 'true'
    });
  });
  
  expect(testContext.contacts.size).to.be.greaterThan(0);
});

// Protocol selection steps
Given('I want to start a conversation with {word}', function(contactName) {
  const contact = testContext.contacts.get(contactName);
  expect(contact).to.exist;
  
  testContext.conversationContext = {
    participants: [testContext.currentUser, contactName],
    participantCount: 2,
    capabilities: {
      supportsOTR: contact.supportsOTR,
      supportsMLs: contact.supportsMLs
    }
  };
});

Given('I want to start a conversation with {word} and {word}', function(contact1, contact2) {
  const c1 = testContext.contacts.get(contact1);
  const c2 = testContext.contacts.get(contact2);
  expect(c1).to.exist;
  expect(c2).to.exist;
  
  testContext.conversationContext = {
    participants: [testContext.currentUser, contact1, contact2],
    participantCount: 3,
    capabilities: {
      supportsOTR: c1.supportsOTR && c2.supportsOTR,
      supportsMLs: c1.supportsMLs && c2.supportsMLs
    }
  };
});

Given('I want to start a conversation with {word}, {word}, and {word}', function(contact1, contact2, contact3) {
  const c1 = testContext.contacts.get(contact1);
  const c2 = testContext.contacts.get(contact2);
  const c3 = testContext.contacts.get(contact3);
  expect(c1).to.exist;
  expect(c2).to.exist;
  expect(c3).to.exist;
  
  testContext.conversationContext = {
    participants: [testContext.currentUser, contact1, contact2, contact3],
    participantCount: 4,
    capabilities: {
      supportsOTR: c1.supportsOTR && c2.supportsOTR && c3.supportsOTR,
      supportsMLs: c1.supportsMLs && c2.supportsMLs && c3.supportsMLs
    }
  };
});

Given('{word} supports both OTR and MLS', function(contactName) {
  const contact = testContext.contacts.get(contactName);
  expect(contact).to.exist;
  expect(contact.supportsOTR).to.be.true;
  expect(contact.supportsMLs).to.be.true;
});

Given('both {word} and {word} support MLS', function(contact1, contact2) {
  const c1 = testContext.contacts.get(contact1);
  const c2 = testContext.contacts.get(contact2);
  expect(c1).to.exist;
  expect(c2).to.exist;
  expect(c1.supportsMLs).to.be.true;
  expect(c2.supportsMLs).to.be.true;
});

Given('{word} only supports OTR', function(contactName) {
  const contact = testContext.contacts.get(contactName);
  expect(contact).to.exist;
  expect(contact.supportsOTR).to.be.true;
  expect(contact.supportsMLs).to.be.false;
});

Given('my policy is set to {string}', function(policyDescription) {
  switch (policyDescription) {
    case 'prefer OTR for 2-party conversations':
      testContext.policy.preferOTRFor2Party = true;
      break;
    default:
      throw new Error(`Unknown policy: ${policyDescription}`);
  }
});

Given('my user preference is set to {string}', function(preference) {
  testContext.userPreference = preference;
});

Given('my security priority is set to {string}', function(priority) {
  testContext.securityPriority = priority;
});

Given('I have a security requirement for {string}', function(requirement) {
  switch (requirement) {
    case 'deniability':
      testContext.securityRequirements.requireDeniability = true;
      break;
    case 'post-compromise security':
      testContext.securityRequirements.requirePostCompromiseSecurity = true;
      break;
    default:
      throw new Error(`Unknown security requirement: ${requirement}`);
  }
});

Given('the conversation type is set to {string}', function(conversationType) {
  testContext.conversationContext.conversationType = conversationType;
});

Given('I have an active OTR conversation with {word}', function(contactName) {
  testContext.conversationContext = {
    participants: [testContext.currentUser, contactName],
    participantCount: 2,
    existingProtocol: 'OTR',
    capabilities: { supportsOTR: true, supportsMLs: true }
  };
});

Given('we have exchanged {int} messages', function(messageCount) {
  // Simulate message exchange
  testContext.messageHistory = Array.from({ length: messageCount }, (_, i) => ({
    id: i + 1,
    content: `Message ${i + 1}`,
    timestamp: new Date(Date.now() - (messageCount - i) * 60000).toISOString()
  }));
});

// Action steps
When('I initiate the conversation', async function() {
  const selector = new ProtocolSelector({
    ...testContext.policy,
    securityPriority: testContext.securityPriority
  });
  
  const context = {
    ...testContext.conversationContext,
    userPreference: testContext.userPreference,
    securityRequirements: testContext.securityRequirements
  };
  
  try {
    testContext.protocolSelection = selector.selectProtocol(context);
    
    // Create session with selected protocol
    testContext.session = new UnifiedSession({
      policy: testContext.policy
    });
    
    await testContext.session.initializeSession(
      context.participants,
      { 
        existingProtocol: context.existingProtocol,
        userPreference: testContext.userPreference,
        securityRequirements: testContext.securityRequirements,
        conversationType: context.conversationType
      }
    );
    
  } catch (error) {
    testContext.error = error;
  }
});

When('I initiate the group conversation', async function() {
  await this.initiate_conversation();
});

When('I attempt to initiate the group conversation', async function() {
  // Same as initiate but expect potential errors
  await this.initiate_conversation();
});

When('I restart the application', function() {
  // Simulate application restart
  testContext.applicationRestarted = true;
});

When('I reconnect to the conversation', async function() {
  // Simulate reconnection with existing protocol
  const selector = new ProtocolSelector(testContext.policy);
  
  const context = {
    ...testContext.conversationContext,
    existingProtocol: 'OTR' // Preserved from before restart
  };
  
  testContext.protocolSelection = selector.selectProtocol(context);
});

When('capability negotiation takes longer than {int} seconds', function(timeoutSeconds) {
  // Simulate timeout scenario
  testContext.capabilityTimeout = timeoutSeconds * 1000;
  testContext.protocolSelection = {
    protocol: 'OTR',
    reason: 'CAPABILITY_TIMEOUT_FALLBACK',
    confidence: 0.3
  };
});

When('any protocol selection is made', function() {
  // This step is used for audit trail scenarios
  testContext.auditEnabled = true;
});

// Assertion steps
Then('{word} should be selected as the protocol', function(expectedProtocol) {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.protocol).to.equal(expectedProtocol);
});

Then('{word} should be selected automatically', function(expectedProtocol) {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.protocol).to.equal(expectedProtocol);
});

Then('I should see {string} in the security indicator', function(expectedIndicator) {
  expect(testContext.session).to.exist;
  const sessionState = testContext.session.getSessionState();
  
  if (expectedIndicator.includes('OTR')) {
    expect(sessionState.protocol).to.equal('OTR');
  } else if (expectedIndicator.includes('MLS')) {
    expect(sessionState.protocol).to.equal('MLS');
  }
});

Then('the conversation should support deniability', function() {
  expect(testContext.session).to.exist;
  const capabilities = testContext.session.currentProtocol.getCapabilities();
  expect(capabilities.supportsDeniability).to.be.true;
});

Then('the conversation should support group messaging', function() {
  expect(testContext.session).to.exist;
  const capabilities = testContext.session.currentProtocol.getCapabilities();
  expect(capabilities.supportsGroupMessaging).to.be.true;
});

Then('all participants should receive group keys', function() {
  // Simulate group key distribution verification
  expect(testContext.session).to.exist;
  expect(testContext.session.currentProtocol.type).to.equal('MLS');
});

Then('I should be able to send encrypted messages', function() {
  expect(testContext.session).to.exist;
  const sessionState = testContext.session.getSessionState();
  expect(sessionState.isActive).to.be.true;
});

Then('the reason should be {string}', function(expectedReason) {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.reason).to.equal(expectedReason);
});

Then('the system should display a capability warning', function() {
  expect(testContext.error).to.exist;
  expect(testContext.error.message).to.include('MLS required');
});

Then('suggest the following alternatives:', function(dataTable) {
  const expectedAlternatives = dataTable.hashes();
  
  // In a real implementation, this would check the UI for suggested alternatives
  expect(expectedAlternatives.length).to.be.greaterThan(0);
  testContext.alternatives = expectedAlternatives;
});

Then('I should be able to choose my preferred option', function() {
  expect(testContext.alternatives).to.exist;
  expect(testContext.alternatives.length).to.be.greaterThan(0);
});

Then('{word} should remain the selected protocol', function(expectedProtocol) {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.protocol).to.equal(expectedProtocol);
});

Then('message history should be preserved', function() {
  expect(testContext.messageHistory).to.exist;
  expect(testContext.messageHistory.length).to.be.greaterThan(0);
});

Then('the session should continue seamlessly', function() {
  expect(testContext.session).to.exist;
  const sessionState = testContext.session.getSessionState();
  expect(sessionState.isActive).to.be.true;
});

Then('deniability should be confirmed as available', function() {
  expect(testContext.session).to.exist;
  const securityProperties = testContext.session.currentProtocol.getSecurityProperties();
  expect(securityProperties.deniability).to.be.true;
});

Then('post-compromise security should be confirmed as available', function() {
  expect(testContext.session).to.exist;
  const securityProperties = testContext.session.currentProtocol.getSecurityProperties();
  expect(securityProperties.postCompromiseSecurity).to.be.true;
});

Then('group messaging features should be available', function() {
  expect(testContext.session).to.exist;
  const capabilities = testContext.session.currentProtocol.getCapabilities();
  expect(capabilities.supportsGroupMessaging).to.be.true;
});

Then('the system should display an incompatibility error', function() {
  expect(testContext.error).to.exist;
  expect(testContext.error.message).to.include('No supported protocols available');
});

Then('suggest upgrading my client to support MLS', function() {
  // In a real implementation, this would check for upgrade suggestions in the UI
  expect(testContext.error).to.exist;
});

Then('offer to notify {word} about the compatibility issue', function(contactName) {
  // In a real implementation, this would check for notification options in the UI
  expect(testContext.error).to.exist;
});

Then('the system should optimize for large group performance', function() {
  expect(testContext.session).to.exist;
  expect(testContext.session.currentProtocol.type).to.equal('MLS');
  
  const performanceChars = testContext.session.currentProtocol.getPerformanceCharacteristics();
  expect(performanceChars.scalability).to.equal('high');
});

Then('tree-based key management should be used', function() {
  expect(testContext.session).to.exist;
  expect(testContext.session.currentProtocol.type).to.equal('MLS');
});

Then('the system should provide a confidence level', function() {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.confidence).to.be.a('number');
  expect(testContext.protocolSelection.confidence).to.be.within(0, 1);
});

Then('list alternative protocols with their reasons', function() {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.alternatives).to.be.an('array');
});

Then('allow me to override the selection if confidence is low', function() {
  expect(testContext.protocolSelection).to.exist;
  if (testContext.protocolSelection.confidence < 0.7) {
    // Low confidence should allow override
    expect(testContext.protocolSelection.alternatives.length).to.be.greaterThan(0);
  }
});

Then('the system should timeout gracefully', function() {
  expect(testContext.capabilityTimeout).to.exist;
  expect(testContext.protocolSelection.reason).to.include('TIMEOUT');
});

Then('fall back to the most compatible protocol', function() {
  expect(testContext.protocolSelection).to.exist;
  expect(testContext.protocolSelection.protocol).to.equal('OTR'); // Most compatible fallback
});

Then('notify me about the degraded capability detection', function() {
  // In a real implementation, this would check for timeout notifications
  expect(testContext.capabilityTimeout).to.exist;
});

Then('the system should log the selection decision', function() {
  if (testContext.auditEnabled) {
    // In a real implementation, this would verify audit log entries
    expect(testContext.protocolSelection).to.exist;
  }
});

Then('include all factors that influenced the decision', function() {
  if (testContext.auditEnabled) {
    expect(testContext.protocolSelection.reason).to.exist;
  }
});

Then('provide a timestamp and session identifier', function() {
  if (testContext.auditEnabled) {
    expect(testContext.session).to.exist;
    expect(testContext.session.sessionId).to.exist;
  }
});

Then('make the audit trail available for review', function() {
  if (testContext.auditEnabled) {
    // In a real implementation, this would verify audit trail accessibility
    expect(testContext.protocolSelection).to.exist;
  }
});

// Helper function to bind context
function bind_context() {
  this.initiate_conversation = async function() {
    await When('I initiate the conversation').call(this);
  };
}

// Bind helper functions to the world context
Before(bind_context);
