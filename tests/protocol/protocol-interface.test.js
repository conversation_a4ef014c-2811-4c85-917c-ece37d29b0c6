/**
 * Test suite for Protocol Interface
 * 
 * Comprehensive tests for the unified protocol interface that both
 * OTR and MLS implementations must follow.
 */

import { 
  ProtocolInterface, 
  PROTOCOL_CAPABILITIES, 
  PROTOCOL_STATES,
  ProtocolError,
  ProtocolSelectionError,
  MigrationError
} from '../../src/core/protocol/ProtocolInterface.js';

// Mock implementations for testing
class MockOTRProtocol extends ProtocolInterface {
  constructor(version = 3) {
    super('OTR', version, PROTOCOL_CAPABILITIES.OTR);
  }

  async initializeSession(participants, options = {}) {
    this.validateParticipants(participants);
    this.participants = [...participants];
    this.sessionId = this.generateSessionId();
    this.setState('INITIALIZING');
    
    // Simulate OTR handshake
    await new Promise(resolve => setTimeout(resolve, 100));
    this.setState('ACTIVE');
  }

  async sendMessage(content, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }
    
    return {
      type: 'OTR_DATA',
      content: `?OTR:${Buffer.from(content).toString('base64')}`,
      encrypted: true,
      timestamp: new Date().toISOString()
    };
  }

  async receiveMessage(message, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }

    if (!message.content.startsWith('?OTR:')) {
      throw new ProtocolError('Invalid OTR message format', 'INVALID_MESSAGE', this.type);
    }

    const encodedContent = message.content.substring(5);
    const content = Buffer.from(encodedContent, 'base64').toString();
    
    return {
      content,
      sender: message.sender || 'unknown',
      verified: true,
      timestamp: message.timestamp
    };
  }

  async addParticipant(participant) {
    throw new ProtocolError('OTR does not support adding participants', 'UNSUPPORTED_OPERATION', this.type);
  }

  async removeParticipant(participant) {
    throw new ProtocolError('OTR does not support removing participants', 'UNSUPPORTED_OPERATION', this.type);
  }

  async verifyParticipant(participant, method = 'fingerprint') {
    return method === 'fingerprint'; // Mock verification
  }

  async endSession() {
    this.setState('TERMINATED');
    await this.cleanup();
  }
}

class MockMLSProtocol extends ProtocolInterface {
  constructor(cipherSuite = 'MLS_128_DHKEMX25519_AES128GCM_SHA256_Ed25519') {
    super('MLS', 1, PROTOCOL_CAPABILITIES.MLS);
    this.cipherSuite = cipherSuite;
    this.epoch = 0;
  }

  async initializeSession(participants, options = {}) {
    this.validateParticipants(participants);
    this.participants = [...participants];
    this.sessionId = this.generateSessionId();
    this.setState('INITIALIZING');
    
    // Simulate MLS group creation
    await new Promise(resolve => setTimeout(resolve, 200));
    this.setState('ACTIVE');
  }

  async sendMessage(content, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }
    
    return {
      type: 'MLS_APPLICATION',
      content: `MLS:${this.epoch}:${Buffer.from(content).toString('base64')}`,
      encrypted: true,
      epoch: this.epoch,
      timestamp: new Date().toISOString()
    };
  }

  async receiveMessage(message, context = {}) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }

    if (!message.content.startsWith('MLS:')) {
      throw new ProtocolError('Invalid MLS message format', 'INVALID_MESSAGE', this.type);
    }

    const parts = message.content.split(':');
    const content = Buffer.from(parts[2], 'base64').toString();
    
    return {
      content,
      sender: message.sender || 'unknown',
      verified: true,
      epoch: parseInt(parts[1]),
      timestamp: message.timestamp
    };
  }

  async addParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }
    
    this.participants.push(participant);
    this.epoch++;
    this.emitProtocolEvent('participantAdded', { participant, newEpoch: this.epoch });
  }

  async removeParticipant(participant) {
    if (this.state !== 'ACTIVE') {
      throw new ProtocolError('Session not active', 'INVALID_STATE', this.type);
    }
    
    const index = this.participants.indexOf(participant);
    if (index > -1) {
      this.participants.splice(index, 1);
      this.epoch++;
      this.emitProtocolEvent('participantRemoved', { participant, newEpoch: this.epoch });
    }
  }

  async verifyParticipant(participant, method = 'credential') {
    return method === 'credential'; // Mock verification
  }

  async endSession() {
    this.setState('TERMINATED');
    await this.cleanup();
  }
}

describe('ProtocolInterface', () => {
  describe('Base Interface', () => {
    test('should throw errors for unimplemented methods', async () => {
      const protocol = new ProtocolInterface('TEST', 1);
      
      await expect(protocol.initializeSession(['alice', 'bob']))
        .rejects.toThrow('initializeSession must be implemented');
      
      await expect(protocol.sendMessage('test'))
        .rejects.toThrow('sendMessage must be implemented');
      
      await expect(protocol.receiveMessage({}))
        .rejects.toThrow('receiveMessage must be implemented');
    });

    test('should provide correct capabilities', () => {
      const otrProtocol = new MockOTRProtocol();
      const mlsProtocol = new MockMLSProtocol();

      const otrCaps = otrProtocol.getCapabilities();
      expect(otrCaps.type).toBe('OTR');
      expect(otrCaps.maxParticipants).toBe(2);
      expect(otrCaps.supportsDeniability).toBe(true);
      expect(otrCaps.supportsGroupMessaging).toBe(false);

      const mlsCaps = mlsProtocol.getCapabilities();
      expect(mlsCaps.type).toBe('MLS');
      expect(mlsCaps.maxParticipants).toBe(10000);
      expect(mlsCaps.supportsDeniability).toBe(false);
      expect(mlsCaps.supportsGroupMessaging).toBe(true);
    });

    test('should provide security properties', () => {
      const otrProtocol = new MockOTRProtocol();
      const mlsProtocol = new MockMLSProtocol();

      const otrSecurity = otrProtocol.getSecurityProperties();
      expect(otrSecurity.confidentiality).toBe(true);
      expect(otrSecurity.deniability).toBe(true);
      expect(otrSecurity.postCompromiseSecurity).toBe(false);

      const mlsSecurity = mlsProtocol.getSecurityProperties();
      expect(mlsSecurity.confidentiality).toBe(true);
      expect(mlsSecurity.deniability).toBe(false);
      expect(mlsSecurity.postCompromiseSecurity).toBe(true);
    });

    test('should check feature support correctly', () => {
      const otrProtocol = new MockOTRProtocol();
      const mlsProtocol = new MockMLSProtocol();

      expect(otrProtocol.supportsFeature('deniability')).toBe(true);
      expect(otrProtocol.supportsFeature('groupMessaging')).toBe(false);

      expect(mlsProtocol.supportsFeature('deniability')).toBe(false);
      expect(mlsProtocol.supportsFeature('groupMessaging')).toBe(true);
      expect(mlsProtocol.supportsFeature('postCompromiseSecurity')).toBe(true);
    });
  });

  describe('OTR Protocol Implementation', () => {
    let otrProtocol;

    beforeEach(() => {
      otrProtocol = new MockOTRProtocol();
    });

    afterEach(async () => {
      if (otrProtocol.state !== 'TERMINATED') {
        await otrProtocol.endSession();
      }
    });

    test('should initialize session correctly', async () => {
      await otrProtocol.initializeSession(['alice', 'bob']);

      expect(otrProtocol.state).toBe('ACTIVE');
      expect(otrProtocol.participants).toEqual(['alice', 'bob']);
      expect(otrProtocol.sessionId).toBeDefined();
      expect(otrProtocol.sessionId).toMatch(/^otr-\d+-[a-z0-9]+$/);
    });

    test('should validate participant count', async () => {
      await expect(otrProtocol.initializeSession(['alice']))
        .rejects.toThrow('At least 2 participants required');

      await expect(otrProtocol.initializeSession(['alice', 'bob', 'charlie']))
        .rejects.toThrow('Protocol OTR supports maximum 2 participants');
    });

    test('should handle message encryption/decryption', async () => {
      await otrProtocol.initializeSession(['alice', 'bob']);

      const message = 'Hello, secure world!';
      const encrypted = await otrProtocol.sendMessage(message);

      expect(encrypted.type).toBe('OTR_DATA');
      expect(encrypted.content).toMatch(/^\?OTR:/);
      expect(encrypted.encrypted).toBe(true);

      const decrypted = await otrProtocol.receiveMessage(encrypted);
      expect(decrypted.content).toBe(message);
      expect(decrypted.verified).toBe(true);
    });

    test('should reject group operations', async () => {
      await otrProtocol.initializeSession(['alice', 'bob']);

      await expect(otrProtocol.addParticipant('charlie'))
        .rejects.toThrow('OTR does not support adding participants');

      await expect(otrProtocol.removeParticipant('bob'))
        .rejects.toThrow('OTR does not support removing participants');
    });

    test('should handle verification', async () => {
      await otrProtocol.initializeSession(['alice', 'bob']);

      const verified = await otrProtocol.verifyParticipant('bob', 'fingerprint');
      expect(verified).toBe(true);
    });
  });

  describe('MLS Protocol Implementation', () => {
    let mlsProtocol;

    beforeEach(() => {
      mlsProtocol = new MockMLSProtocol();
    });

    afterEach(async () => {
      if (mlsProtocol.state !== 'TERMINATED') {
        await mlsProtocol.endSession();
      }
    });

    test('should initialize group session correctly', async () => {
      await mlsProtocol.initializeSession(['alice', 'bob', 'charlie']);

      expect(mlsProtocol.state).toBe('ACTIVE');
      expect(mlsProtocol.participants).toEqual(['alice', 'bob', 'charlie']);
      expect(mlsProtocol.sessionId).toBeDefined();
      expect(mlsProtocol.epoch).toBe(0);
    });

    test('should handle group message encryption/decryption', async () => {
      await mlsProtocol.initializeSession(['alice', 'bob', 'charlie']);

      const message = 'Hello, group!';
      const encrypted = await mlsProtocol.sendMessage(message);

      expect(encrypted.type).toBe('MLS_APPLICATION');
      expect(encrypted.content).toMatch(/^MLS:\d+:/);
      expect(encrypted.encrypted).toBe(true);
      expect(encrypted.epoch).toBe(0);

      const decrypted = await mlsProtocol.receiveMessage(encrypted);
      expect(decrypted.content).toBe(message);
      expect(decrypted.verified).toBe(true);
      expect(decrypted.epoch).toBe(0);
    });

    test('should support adding participants', async () => {
      await mlsProtocol.initializeSession(['alice', 'bob']);

      const eventPromise = new Promise(resolve => {
        mlsProtocol.once('participantAdded', resolve);
      });

      await mlsProtocol.addParticipant('charlie');

      expect(mlsProtocol.participants).toContain('charlie');
      expect(mlsProtocol.epoch).toBe(1);

      const event = await eventPromise;
      expect(event.participant).toBe('charlie');
      expect(event.newEpoch).toBe(1);
    });

    test('should support removing participants', async () => {
      await mlsProtocol.initializeSession(['alice', 'bob', 'charlie']);

      const eventPromise = new Promise(resolve => {
        mlsProtocol.once('participantRemoved', resolve);
      });

      await mlsProtocol.removeParticipant('charlie');

      expect(mlsProtocol.participants).not.toContain('charlie');
      expect(mlsProtocol.epoch).toBe(1);

      const event = await eventPromise;
      expect(event.participant).toBe('charlie');
      expect(event.newEpoch).toBe(1);
    });
  });

  describe('Protocol Compatibility', () => {
    test('should check compatibility correctly', () => {
      const otr1 = new MockOTRProtocol(3);
      const otr2 = new MockOTRProtocol(3);
      const otr3 = new MockOTRProtocol(2);
      const mls1 = new MockMLSProtocol();

      expect(otr1.isCompatibleWith(otr2)).toBe(true);
      expect(otr1.isCompatibleWith(otr3)).toBe(false);
      expect(otr1.isCompatibleWith(mls1)).toBe(false);
    });

    test('should provide migration compatibility info', () => {
      const otrProtocol = new MockOTRProtocol();
      const mlsProtocol = new MockMLSProtocol();

      const otrToMls = otrProtocol.getMigrationCompatibility('MLS');
      expect(otrToMls.canMigrateTo).toBe(true);
      expect(otrToMls.preservedProperties).toContain('forwardSecrecy');
      expect(otrToMls.lostProperties).toContain('deniability');

      const mlsToOtr = mlsProtocol.getMigrationCompatibility('OTR');
      expect(mlsToOtr.canMigrateTo).toBe(true);
      expect(mlsToOtr.preservedProperties).toContain('forwardSecrecy');
      expect(mlsToOtr.lostProperties).toContain('groupMessaging');
    });
  });

  describe('Session State Management', () => {
    test('should track session state correctly', async () => {
      const protocol = new MockOTRProtocol();

      expect(protocol.getSessionState().state).toBe('UNINITIALIZED');
      expect(protocol.getSessionState().isActive).toBe(false);

      await protocol.initializeSession(['alice', 'bob']);

      const state = protocol.getSessionState();
      expect(state.state).toBe('ACTIVE');
      expect(state.isActive).toBe(true);
      expect(state.isSecure).toBe(true);
      expect(state.participants).toEqual(['alice', 'bob']);
    });

    test('should emit state change events', async () => {
      const protocol = new MockOTRProtocol();
      const stateChanges = [];

      protocol.on('stateChanged', (event) => {
        stateChanges.push(event);
      });

      await protocol.initializeSession(['alice', 'bob']);

      expect(stateChanges).toHaveLength(2);
      expect(stateChanges[0].newState).toBe('INITIALIZING');
      expect(stateChanges[1].newState).toBe('ACTIVE');
    });
  });

  describe('Error Handling', () => {
    test('should throw appropriate errors', async () => {
      const protocol = new MockOTRProtocol();

      // Test sending message before initialization
      await expect(protocol.sendMessage('test'))
        .rejects.toThrow(ProtocolError);

      // Test invalid participant validation
      await expect(protocol.initializeSession([]))
        .rejects.toThrow('At least 2 participants required');

      // Test duplicate participants
      await expect(protocol.initializeSession(['alice', 'alice']))
        .rejects.toThrow('Duplicate participants not allowed');
    });
  });

  describe('Cleanup and Resource Management', () => {
    test('should clean up resources properly', async () => {
      const protocol = new MockOTRProtocol();
      await protocol.initializeSession(['alice', 'bob']);

      expect(protocol.state).toBe('ACTIVE');
      expect(protocol.participants).toHaveLength(2);

      await protocol.cleanup();

      expect(protocol.state).toBe('TERMINATED');
      expect(protocol.participants).toHaveLength(0);
      expect(protocol.sessionId).toBeNull();
    });
  });
});
