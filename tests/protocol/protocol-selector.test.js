/**
 * Test suite for Protocol Selector
 * 
 * Comprehensive tests for intelligent protocol selection logic,
 * migration feasibility analysis, and policy-driven decision making.
 */

import { ProtocolSelector } from '../../src/core/protocol/ProtocolSelector.js';
import { ProtocolSelectionError } from '../../src/core/protocol/ProtocolInterface.js';

describe('ProtocolSelector', () => {
  let selector;

  beforeEach(() => {
    selector = new ProtocolSelector({
      preferOTRFor2Party: true,
      requireMLSForGroups: true,
      allowProtocolMigration: true,
      respectUserPreference: true,
      securityPriority: 'balanced'
    });
  });

  describe('Protocol Selection Logic', () => {
    test('should select OTR for two-party conversations by default', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('OTR');
      expect(result.reason).toBe('POLICY_DEFAULT_2PARTY');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.alternatives).toContain('MLS');
    });

    test('should require MLS for group conversations', () => {
      const context = {
        participantCount: 3,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('MLS');
      expect(result.reason).toBe('GROUP_CONVERSATION');
      expect(result.confidence).toBe(1.0);
      expect(result.alternatives).toHaveLength(0);
    });

    test('should throw error when MLS required but not supported', () => {
      const context = {
        participantCount: 3,
        capabilities: { supportsOTR: true, supportsMLs: false }
      };

      expect(() => selector.selectProtocol(context))
        .toThrow(ProtocolSelectionError);
      
      expect(() => selector.selectProtocol(context))
        .toThrow('MLS required for group conversations but not supported by all participants');
    });

    test('should respect user preference when valid', () => {
      const context = {
        participantCount: 2,
        userPreference: 'MLS',
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('MLS');
      expect(result.reason).toBe('USER_PREFERENCE');
      expect(result.confidence).toBe(0.8);
    });

    test('should preserve existing protocol when possible', () => {
      const context = {
        participantCount: 2,
        existingProtocol: 'OTR',
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('OTR');
      expect(result.reason).toBe('EXISTING_SESSION');
      expect(result.confidence).toBe(0.9);
    });

    test('should handle security requirement for deniability', () => {
      const context = {
        participantCount: 2,
        securityRequirements: { requireDeniability: true },
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('OTR');
      expect(result.reason).toBe('SECURITY_REQUIREMENT_DENIABILITY');
      expect(result.confidence).toBe(0.9);
    });

    test('should handle security requirement for post-compromise security', () => {
      const context = {
        participantCount: 2,
        securityRequirements: { requirePostCompromiseSecurity: true },
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('MLS');
      expect(result.reason).toBe('SECURITY_REQUIREMENT_PCS');
      expect(result.confidence).toBe(0.9);
    });

    test('should prefer MLS for meeting conversation type', () => {
      const context = {
        participantCount: 2,
        conversationType: 'meeting',
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('MLS');
      expect(result.reason).toBe('CONVERSATION_TYPE_MEETING');
      expect(result.confidence).toBe(0.7);
    });

    test('should handle security priority settings', () => {
      const privacySelector = new ProtocolSelector({ securityPriority: 'privacy' });
      const securitySelector = new ProtocolSelector({ securityPriority: 'security' });

      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const privacyResult = privacySelector.selectProtocol(context);
      expect(privacyResult.protocol).toBe('OTR');
      expect(privacyResult.reason).toBe('SECURITY_PRIORITY_PRIVACY');

      const securityResult = securitySelector.selectProtocol(context);
      expect(securityResult.protocol).toBe('MLS');
      expect(securityResult.reason).toBe('SECURITY_PRIORITY_SECURITY');
    });

    test('should fallback to first available protocol when no rules match', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: false, supportsMLs: true }
      };

      const result = selector.selectProtocol(context);
      
      expect(result.protocol).toBe('MLS');
      expect(result.reason).toBe('FALLBACK_FIRST_AVAILABLE');
      expect(result.confidence).toBe(0.3);
    });
  });

  describe('Input Validation', () => {
    test('should validate participant count', () => {
      const invalidContexts = [
        { participantCount: 0 },
        { participantCount: 1 },
        { participantCount: -1 }
      ];

      invalidContexts.forEach(context => {
        expect(() => selector.selectProtocol(context))
          .toThrow(ProtocolSelectionError);
      });
    });

    test('should validate participant count consistency', () => {
      const context = {
        participantCount: 3,
        participants: ['alice', 'bob'] // Mismatch: count=3 but only 2 participants
      };

      expect(() => selector.selectProtocol(context))
        .toThrow('Participant count mismatch');
    });

    test('should handle missing capabilities gracefully', () => {
      const context = {
        participantCount: 2,
        capabilities: {} // No explicit capabilities
      };

      const result = selector.selectProtocol(context);
      expect(result.protocol).toBe('OTR'); // Should default to OTR
    });
  });

  describe('Migration Feasibility', () => {
    test('should allow OTR to MLS migration for 2+ participants', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = selector.canMigrate('OTR', 'MLS', context);
      
      expect(result.canMigrate).toBe(true);
      expect(result.reason).toBe('MIGRATION_FEASIBLE');
      expect(result.preservedProperties).toContain('forwardSecrecy');
      expect(result.lostProperties).toContain('deniability');
    });

    test('should allow MLS to OTR migration for 2 participants only', () => {
      const twoPartyContext = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const groupContext = {
        participantCount: 3,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const twoPartyResult = selector.canMigrate('MLS', 'OTR', twoPartyContext);
      expect(twoPartyResult.canMigrate).toBe(true);

      const groupResult = selector.canMigrate('MLS', 'OTR', groupContext);
      expect(groupResult.canMigrate).toBe(false);
      expect(groupResult.reason).toBe('PARTICIPANT_COUNT_EXCEEDS_LIMIT');
    });

    test('should reject migration when policy disallows it', () => {
      const restrictiveSelector = new ProtocolSelector({ allowProtocolMigration: false });
      
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = restrictiveSelector.canMigrate('OTR', 'MLS', context);
      
      expect(result.canMigrate).toBe(false);
      expect(result.reason).toBe('POLICY_DISALLOWS_MIGRATION');
    });

    test('should check capability requirements for migration', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: false }
      };

      const result = selector.canMigrate('OTR', 'MLS', context);
      
      expect(result.canMigrate).toBe(false);
      expect(result.reason).toBe('INSUFFICIENT_CAPABILITIES');
      expect(result.requirements).toContain('All participants must support MLS protocol');
    });
  });

  describe('Migration Planning', () => {
    test('should create migration plan for valid migration', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const plan = selector.planMigration('OTR', 'MLS', context);
      
      expect(plan.fromProtocol).toBe('OTR');
      expect(plan.toProtocol).toBe('MLS');
      expect(plan.steps).toBeDefined();
      expect(plan.steps.length).toBeGreaterThan(0);
      expect(plan.estimatedDuration).toBeGreaterThan(0);
      expect(plan.risks).toBeDefined();
      expect(plan.rollbackPlan).toBeDefined();
    });

    test('should throw error for infeasible migration', () => {
      const context = {
        participantCount: 3,
        capabilities: { supportsOTR: true, supportsMLs: false }
      };

      expect(() => selector.planMigration('OTR', 'MLS', context))
        .toThrow(ProtocolSelectionError);
    });

    test('should include protocol-specific migration steps', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const plan = selector.planMigration('OTR', 'MLS', context);
      
      const stepTypes = plan.steps.map(step => step.step);
      expect(stepTypes).toContain('VALIDATE_PREREQUISITES');
      expect(stepTypes).toContain('NOTIFY_PARTICIPANTS');
      expect(stepTypes).toContain('PREPARE_NEW_PROTOCOL');
      expect(stepTypes).toContain('EXCHANGE_KEYS');
      expect(stepTypes).toContain('ACTIVATE_PROTOCOL');
      expect(stepTypes).toContain('CLEANUP_OLD_PROTOCOL');
    });

    test('should estimate migration duration based on participant count', () => {
      const smallGroupContext = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const largeGroupContext = {
        participantCount: 10,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const smallGroupPlan = selector.planMigration('OTR', 'MLS', smallGroupContext);
      const largeGroupPlan = selector.planMigration('OTR', 'MLS', largeGroupContext);
      
      expect(largeGroupPlan.estimatedDuration).toBeGreaterThan(smallGroupPlan.estimatedDuration);
    });

    test('should assess migration risks', () => {
      const largeGroupContext = {
        participantCount: 15,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const plan = selector.planMigration('OTR', 'MLS', largeGroupContext);
      
      expect(plan.risks).toBeDefined();
      expect(plan.risks.length).toBeGreaterThan(0);
      
      const coordinationRisk = plan.risks.find(risk => risk.type === 'COORDINATION_COMPLEXITY');
      expect(coordinationRisk).toBeDefined();
      expect(coordinationRisk.severity).toBe('medium');
    });

    test('should create rollback plan', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const plan = selector.planMigration('OTR', 'MLS', context);
      
      expect(plan.rollbackPlan).toBeDefined();
      expect(plan.rollbackPlan.triggerConditions).toContain('MIGRATION_TIMEOUT');
      expect(plan.rollbackPlan.triggerConditions).toContain('PARTICIPANT_FAILURE');
      expect(plan.rollbackPlan.steps).toContain('HALT_MIGRATION');
      expect(plan.rollbackPlan.steps).toContain('RESTORE_ORIGINAL_PROTOCOL');
      expect(plan.rollbackPlan.timeoutDuration).toBe(30000);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle unknown protocol types gracefully', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      expect(() => selector.canMigrate('UNKNOWN', 'MLS', context))
        .not.toThrow();
      
      const result = selector.canMigrate('UNKNOWN', 'MLS', context);
      expect(result.canMigrate).toBe(false);
    });

    test('should validate protocol selection results', () => {
      const context = {
        participantCount: 5000, // Exceeds MLS max participants
        capabilities: { supportsOTR: false, supportsMLs: true }
      };

      expect(() => selector.selectProtocol(context))
        .toThrow(ProtocolSelectionError);
    });

    test('should handle empty capabilities object', () => {
      const context = {
        participantCount: 2,
        capabilities: {}
      };

      const result = selector.selectProtocol(context);
      expect(result.protocol).toBe('OTR'); // Should default to OTR
    });

    test('should handle null/undefined capabilities', () => {
      const context = {
        participantCount: 2,
        capabilities: null
      };

      expect(() => selector.selectProtocol(context))
        .not.toThrow();
    });
  });

  describe('Policy Configuration', () => {
    test('should respect custom policy settings', () => {
      const customSelector = new ProtocolSelector({
        preferOTRFor2Party: false,
        requireMLSForGroups: true,
        securityPriority: 'security'
      });

      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = customSelector.selectProtocol(context);
      expect(result.protocol).toBe('MLS'); // Should prefer MLS due to security priority
    });

    test('should handle conflicting policy settings gracefully', () => {
      const conflictingSelector = new ProtocolSelector({
        preferOTRFor2Party: true,
        securityPriority: 'security' // Conflicts with OTR preference
      });

      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const result = conflictingSelector.selectProtocol(context);
      expect(result.protocol).toBeDefined();
      expect(['OTR', 'MLS']).toContain(result.protocol);
    });
  });

  describe('Performance and Scalability', () => {
    test('should handle large participant lists efficiently', () => {
      const largeContext = {
        participantCount: 1000,
        participants: Array.from({ length: 1000 }, (_, i) => `user${i}`),
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      const startTime = Date.now();
      const result = selector.selectProtocol(largeContext);
      const duration = Date.now() - startTime;

      expect(result.protocol).toBe('MLS');
      expect(duration).toBeLessThan(100); // Should complete quickly
    });

    test('should cache migration feasibility results', () => {
      const context = {
        participantCount: 2,
        capabilities: { supportsOTR: true, supportsMLs: true }
      };

      // First call
      const startTime1 = Date.now();
      const result1 = selector.canMigrate('OTR', 'MLS', context);
      const duration1 = Date.now() - startTime1;

      // Second call (should be faster if cached)
      const startTime2 = Date.now();
      const result2 = selector.canMigrate('OTR', 'MLS', context);
      const duration2 = Date.now() - startTime2;

      expect(result1).toEqual(result2);
      // Note: Actual caching implementation would make duration2 < duration1
    });
  });
});
